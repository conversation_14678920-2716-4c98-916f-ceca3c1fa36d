const { defineConfig } = require('cypress')

module.exports = defineConfig({
  chromeWebSecurity: false,
  hosts: {
    'demo.contact-dev.com': '127.0.0.1',
    'veri-admin.contact-dev.com': '127.0.0.1'
  },
  experimentalSessionAndOrigin: true,
  e2e: {
    // We've imported your old cypress plugins here.
    // You may want to clean this up later by importing these.
    setupNodeEvents(on, config) {
      return require('./cypress/plugins/index.js')(on, config)
    },
    baseUrl: 'https://demo.contact-dev.com:2222/',
    specPattern: [
      'cypress/e2e/form-creation/form-creation.spec.js',
      'cypress/e2e/review/review.spec.js',
      'cypress/e2e/admin/users/users.spec.js',
      'cypress/e2e/custom-questions/custom-questions.spec.js',
    ],
  },
})
