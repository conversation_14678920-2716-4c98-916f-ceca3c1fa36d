Feature: Custom Questions

  @e2e @custom-questions
  Scenario: The user ensures non-live questions do not appear in form
    When The user ensures non-live questions do not appear in form

  @e2e @custom-questions
  Scenario: The user adds questions, goes live, and views them in new report
    When The user adds questions, goes live, and views them in new report

  @e2e @custom-questions
  Scenario: The user goes offline and creates a form, submitting it for review
    When The user goes offline and creates a form, submitting it for review