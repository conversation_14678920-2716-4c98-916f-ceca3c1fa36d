Feature: Review

  @e2e @review
  Scenario: The user opens the review dialog
    When The user opens the review dialog

  @e2e @review
  Scenario: The user approves a report
    When The user approves a report

  @e2e @review
  Scenario: The user approves a report by clicking the approve icon
    When The user approves a report by clicking the approve icon

  @e2e @review
  Scenario: The user denies a report
    When The user denies a report

  @e2e @review
  Scenario: The user edits a report location
    When The user edits a report location

  @e2e @review
  Scenario: The user edits a search description
    When The user edits a search description

  @e2e @review
  Scenario: The user edits stop descriptions
    When The user edits stop descriptions
