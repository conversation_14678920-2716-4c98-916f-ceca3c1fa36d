import { Before, When } from '@badeball/cypress-cucumber-preprocessor';
import { clearSnackBars, getByTestId, type } from '../../../support/utility';
import { logInAs, logInAsAdmin } from '../../../support/actions';

Before(() => {
  logInAsAdmin();
});

When('The user adds a user', () => {
  // Click 'add user' button
  getByTestId('users-add-user').click();

  // Click submit to test validation of top fields
  getByTestId('confirm-dialog-yes-button').click();

  // 5 empty fields above and password below generate errors
  getByTestId('label-field-error').should('have.length', 6);

  // Insert less than 9 digit number into officer id to generate another error
  type('add-user-officer-id', '53179');

  // Expect 7 errors now
  getByTestId('label-field-error').should('have.length', 7);

  // Make officer id 10 digits and years of experience 99
  getByTestId('add-user-officer-id').clear().type('0123456789').should('have.value', '0123456789');
  type('add-user-years', '99');

  // Still expect 7 errors
  getByTestId('label-field-error').should('have.length', 7);

  // Fill in first and last name
  type('add-user-name-first-name', 'Bob');
  type('add-user-name-last-name', 'Barker');

  // Fill in email
  type('add-user-email', '<EMAIL>');

  // Fill in username
  type('add-user-username', 'ThePriceIsWrong1');

  // Erase officer id (to autogenerate)
  getByTestId('add-user-officer-id').clear();

  // Fill in years of experience
  getByTestId('add-user-years').clear().type('10').should('have.value', '10');

  // Uncheck officer role
  cy.get('.add-user__checkbox-officer').children().first().click();

  // Check that no password verifications are passing
  cy.get('.checked').should('have.length', 0);

  // Add password that fulfills requirements
  type('add-user-password', 'Test123!');

  // Check that all four verifications pass
  cy.get('.checked').should('have.length', 4);

  // Check that error for passwords don't match appears
  cy.get('.reset-password-confirm-error-text').should('have.length', 1);

  // Input confirmation password
  type('add-user-confirm-password', 'Test123!');

  // Check that error for passwords don't match doesn't appear
  cy.get('.reset-password-confirm-error-text').should('not.be.visible');

  // Try to submit
  getByTestId('confirm-dialog-yes-button').click();

  // Check that snack has appeared for no role selected
  getByTestId('snackbar-box-1-1').should('be.visible');
  clearSnackBars();

  // Check officer role
  cy.get('.add-user__checkbox-officer').children().first().click();

  // Choose a reviewer
  getByTestId('add-user-dropdown-reviewer').click();
  cy.get('.MuiListItem-root').first().click();
  cy.get('body').click(0, 0); // click outside the menu to close it
  cy.get('.MuiPopover-root').should('not.exist');
  // Submit add user
  // getByTestId('confirm-dialog-yes-button').click({ force: true });
  getByTestId('confirm-dialog-yes-button').click();

  getByTestId('loading').should('not.exist');
  // Sort by date created
  getByTestId('column-7').should('be.visible').click();

  // Check that first row has name Bob Barker
  cy.get('*[data-testid^="users-row-0-"] .users-row__cell-name').should('have.text', 'Bob Barker');

  // Check new user login
  logInAs('ThePriceIsWrong1', 'Test123!');
  cy.url().should('include', '/dashboard');
});

When('The user edits a user', () => {
  // Sort by date created
  getByTestId('column-7').click();
  getByTestId('loading').should('not.exist');

  // Click edit button
  cy.get('*[data-testid^="users-row-0-"] .users-row__table-cell-actions-edit').click();

  // Check that officer id field is disabled
  getByTestId('edit-user-officer-id').should('be.disabled');

  // Change roles to reviewer
  cy.get('.edit-user__checkbox-officer').children().first().click();
  cy.get('.edit-user__checkbox-reviewer').children().first().click();

  // Change password
  getByTestId('edit-user-change-password-toggle').click();
  type('edit-user-password', 'Pass123!');
  type('edit-user-confirm-password', 'Pass123!');

  // Submit changes
  getByTestId('confirm-dialog-yes-button').click();
  getByTestId('loading').should('not.exist');

  cy.get('*[data-testid^="users-row-0-"] .users-row__cell-roles').should('include.text', 'reviewer');

  // // Check updated user credentials
  logInAs('ThePriceIsWrong1', 'Pass123!', '/review');
  cy.url().should('include', '/review');
});

When('The user disables a user', () => {
  // Sort by date created
  getByTestId('column-7').click();

  // Click disable button
  cy.get('*[data-testid^="users-row-0-"] div[title="Disable User"]:not(.disabled)').click();

  // Confirm disable
  getByTestId('confirm-dialog-yes-button').click();

  getByTestId('loading').should('not.exist');

  // Check that status has been changed to disabled
  cy.get('*[data-testid^="users-row-0-"] .users-row__cell-status').should('have.text', 'Disabled');

  // Check that disable button is disabled
  cy.get('*[data-testid^="users-row-0-"] div[title="Disable User"]').should('have.class', 'disabled');

  // Check that user can't login again
  cy.session('failedLogin', () => {});
  cy.visit('https://demo.contact-dev.com:2222/log_in');
  type('login-username-field', 'ThePriceIsWrong1');
  type('login-pw-field', 'Pass123!');

  getByTestId('login-submit').click();

  cy.get('div.auth-provider__loading').should('not.exist');
  getByTestId('loading').should('not.exist');

  cy.contains('Username or password incorrect');
  cy.url().should('include', '/log_in');
});
