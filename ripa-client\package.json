{"name": "ripa-client", "version": "0.1.0", "private": true, "dependencies": {"@babel/polyfill": "^7.12.1", "@date-io/date-fns": "1.3.13", "@googlemaps/js-api-loader": "^1.11.1", "@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "^4.0.0-alpha.56", "@material-ui/pickers": "^3.2.10", "@sentry/browser": "^9.14.0", "@sentry/react": "^9.14.0", "@testing-library/jest-dom": "^5.11.4", "axios": "^1.8.3", "bfj": "^7.0.2", "camelcase": "^6.3.0", "classnames": "^2.5.1", "core-js": "^3.22.8", "cypress": "^14.4.1", "date-fns": "^2.28.0", "dayjs": "^1.11.2", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "emoji-regex": "^9.2.2", "fs-extra": "^10.1.0", "identity-obj-proxy": "3.0.0", "inobounce": "^0.2.1", "json-formatter-js": "^2.3.4", "localforage": "^1.9.0", "lodash": "^4.17.21", "match-sorter": "^6.3.4", "nanoid": "^3.3.8", "opentable": "^6.3.1", "p-iteration": "^1.1.8", "papaparse": "^5.3.2", "prop-types": "^15.8.1", "qs": "^6.10.3", "ramda": "^0.28.0", "react": "^17.0.2", "react-app-polyfill": "^3.0.0", "react-contenteditable": "^3.3.6", "react-dev-utils": "12.0.1", "react-dom": "^17.0.1", "react-redux": "^8.0.2", "react-refresh": "^0.13.0", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "recharts": "^2.1.16", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.1", "resolve": "1.22.0", "semver": "^7.5.3", "uuid": "^8.3.2", "web-vitals": "^2.1.4", "workbox-background-sync": "^6.5.3", "workbox-broadcast-update": "^6.5.3", "workbox-cacheable-response": "^6.5.3", "workbox-core": "^6.5.3", "workbox-expiration": "^6.5.3", "workbox-google-analytics": "^6.5.3", "workbox-navigation-preload": "^6.5.3", "workbox-precaching": "^6.5.3", "workbox-range-requests": "^6.5.3", "workbox-routing": "^6.5.3", "workbox-strategies": "^6.5.3", "workbox-streams": "^6.5.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-object-rest-spread": "^7.25.9", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@badeball/cypress-cucumber-preprocessor": "^22.0.1", "@cypress/webpack-preprocessor": "^6.0.4", "@eslint/compat": "^1.2.8", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.1", "@pmmmwh/react-refresh-webpack-plugin": "0.5.16", "@svgr/webpack": "6.5.1", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^14.2.0", "@types/google.maps": "^3.58.1", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.16", "@types/papaparse": "^5.3.15", "@types/ramda": "^0.28.25", "@types/react": "^17.0.85", "@types/react-dom": "^17.0.26", "@types/react-redux": "~7.1.34", "@types/react-router-dom": "^5.3.3", "@types/react-test-renderer": "^18.3.1", "@types/redux-first-router": "~2.1.12", "@types/redux-form": "~8.3.11", "@types/redux-logger": "~3.0.13", "@types/redux-mock-store": "^1.0.3", "@types/resize-observer-browser": "^0.1.11", "@types/uuid": "^8.3.4", "babel-jest": "^28.1.3", "babel-loader": "8.4.1", "babel-plugin-explicit-exports-references": "^1.0.2", "babel-plugin-macros": "^3.1.0", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.1.0", "case-sensitive-paths-webpack-plugin": "2.4.0", "concurrently": "^7.6.0", "csp-html-webpack-plugin": "^5.1.0", "css-loader": "6.11.0", "css-minimizer-webpack-plugin": "^4.2.2", "dotenv-webpack": "^7.1.1", "eslint": "^9.25.1", "eslint-import-resolver-typescript": "^4.3.4", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-lodash": "^8.0.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-testing-library": "^7.1.1", "eslint-webpack-plugin": "^5.0.1", "express": "^4.21.2", "git-revision-webpack-plugin": "^5.0.0", "globals": "^16.0.0", "html-webpack-plugin": "^5.6.3", "https": "^1.0.0", "husky": "^4.3.8", "jest": "27.5.1", "jest-circus": "27.5.1", "jest-extended": "^2.1.0", "jest-resolve": "27.5.1", "jest-watch-typeahead": "1.0.0", "lint-staged": "^15.5.1", "mini-css-extract-plugin": "^2.9.2", "path-browserify": "^1.0.1", "postcss": "^8.5.3", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^4.3.0", "postcss-normalize": "^13.0.1", "postcss-preset-env": "^10.1.6", "prettier": "^3.5.3", "process": "^0.11.10", "resolve-url-loader": "^5.0.0", "sass": "^1.87.0", "sass-loader": "13.3.3", "sass-resources-loader": "^2.2.5", "stream-browserify": "^3.0.0", "style-loader": "3.3.4", "stylelint": "^16.19.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-webpack-plugin": "^5.0.1", "ts-loader": "^9.5.2", "ts-pnp": "1.2.0", "tsconfig-paths-webpack-plugin": "^3.5.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "url": "^0.11.4", "webpack": "5.99.7", "webpack-cli": "^4.10.0", "webpack-dev-server": "4.9.3", "webpack-manifest-plugin": "^5.0.0", "workbox-webpack-plugin": "^7.3.0"}, "resolutions": {"react-error-overlay": "6.0.9", "@types/node": "^18.0.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.1", "d3-color": "3.1.0", "csp-html-webpack-plugin/cheerio": "1.0.0-rc.10"}, "scripts": {"//": "Run the following script to run a production build locally", "run-prd-ssl": "yarn lint && node scripts/run-prd-ssl.js", "///": "Run the following script to run a dev build over ssl", "startssl": "PORT=${PORT:-2222} HTTPS=true SSL_CRT_FILE='../certs/_wildcard.contact-dev.com.pem' SSL_KEY_FILE='../certs/_wildcard.contact-dev.com-key.pem' node scripts/start.js", "start": "node scripts/start.js", "build": "node scripts/build.js", "test": "yarn lint && TZ='America/Los_Angeles' node scripts/test.js --coverage --watchAll", "lint": "concurrently \"yarn run lint:tsc\" \"yarn run lint:all\" \"yarn run lint:styles\"", "lint:ts": "eslint \"./src/**/*.{ts,tsx}\" --max-warnings 0", "lint:tsc": "tsc -p tsconfig.json --noEmit --checkJs false", "lint:js": "eslint \"./src/**/!(*.test).js\" --max-warnings 0", "lint:all": "eslint \"./src/**/!(*.test).{ts,tsx,js}\" --max-warnings 0", "lint:fix": "eslint \"./src/**/!(*.test).{ts,tsx,js}\" --max-warnings 0 --fix", "lint:styles": "stylelint \"src/**/*.scss\"", "cy:open": "cypress open", "cy:run": "cypress run"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "coveragePathIgnorePatterns": ["node_modules", "src/theme/", "src/setupTests.js", ".styles.js", "store.js", "index.js"], "coverageThreshold": {"global": {"branches": 40, "functions": 60, "lines": 60, "statements": 60}}, "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "testRunner": "<rootDir>/node_modules/jest-circus/runner.js", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy", "^@pages(.*)$": "<rootDir>/src/pages//$1", "^@components(.*)$": "<rootDir>/src/components/$1", "^@engine(.*)$": "<rootDir>/src/engine/$1", "^@ducks(.*)$": "<rootDir>/src/engine/ducks/$1", "^@theme(.*)$": "<rootDir>/src/theme/$1", "^@utility(.*)$": "<rootDir>/src/utility/$1", "^@src(.*)$": "<rootDir>/src/$1", "d3-color": "<rootDir>/node_modules/d3-color/dist/d3-color.min.js", "axios": "axios/dist/node/axios.cjs"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn lint-staged && TZ='America/Los_Angeles' node scripts/test.js --coverage --watchAll=false"}}, "lint-staged": {"*.{js,css,scss,md,json}": ["git add"], "./": ["eslint --max-warnings 0", "git add"], "./**/*.{scss,css}": ["stylelint", "git add"]}, "prettier": {"singleQuote": true, "arrowParens": "always", "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "bracketSameLine": false, "jsxSingleQuote": false, "printWidth": 200, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false, "vueIndentScriptAndStyle": false}, "packageManager": "yarn@4.9.1", "stepDefinitions": "cypress/e2e/**/[filepath]/*.{js,ts}"}