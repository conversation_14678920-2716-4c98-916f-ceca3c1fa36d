.snackbar {
  position: relative;
  width: 400px;
  height: 0;
}

.snackbar__mobile-clickoff {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.snackbar__box {
  position: absolute;
  width: 400px;
  min-height: #{$snackHeight}px;
  padding: 16px 23px 16px 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: $white;
  opacity: 1;
  transition: top 200ms, opacity 2000ms;
  z-index: $snack-z;

  &.success {
    color: $green-60;
    border-top: solid 3px $green-20;
    border-right: solid 1px $green-30;
    border-bottom: solid 1px $green-30;
    border-left: solid 1px $green-30;
  }
  &.error {
    color: $red-60;
    border-top: solid 3px $red-50;
    border-right: solid 1px $red-50;
    border-bottom: solid 1px $red-50;
    border-left: solid 1px $red-50;
  }
  &.warning {
    color: $yellow-80;
    border-top: solid 3px $yellow-40;
    border-right: solid 1px $yellow-40;
    border-bottom: solid 1px $yellow-40;
    border-left: solid 1px $yellow-40;
  }
  &.info {
    color: $teal-60;
    border-top: solid 3px $teal-50;
    border-right: solid 1px $teal-50;
    border-bottom: solid 1px $teal-50;
    border-left: solid 1px $teal-50;
  }
  &.fading {
    opacity: 0;
  }
  &.dead {
    opacity: 0;
  }
  .snackbar__box-close {
    position: absolute;
    top: 0;
    right: 0;
    margin: 6.5px;
    color: $grey-60;
    cursor: pointer;
  }
  .snackbar__box-icon {
    position: absolute;
    top: 15px;
    left: 17px;
  }
  .snackbar__box-title {
    margin: 0;
    line-height: 22px;
  }
  .snackbar__box-body {
    margin-top: 4px;
    font-size: 15px;
    line-height: 16px;
    color: $black;
    white-space: initial;
  }
}

.xs {
  .snackbar__mobile-clickoff {
    display: block;
  }
}
