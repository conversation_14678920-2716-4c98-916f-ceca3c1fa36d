import { connect, ConnectedProps } from 'react-redux';
import { User, Review } from '@ducks';

const mapDispatchToProps = {
  setReviewFeedbackDialogOpen: Review.setReviewFeedbackDialogOpen,
  setReviewFeedback: Review.setReviewFeedback,
  submitReviewFeedback: Review.submitReviewFeedback,
  updateForm: Review.updateForm,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mapStateToProps = (state: any) => ({
  reviewFeedbackDialog: Review.selectors.reviewFeedbackDialog(state) as {
    open: boolean;
    reviewFeedback: string;
    formId: string;
    isReadOnly: boolean;
    reviewer_notes: {
      noteForReviewee: string;
      noteHistory: {
        date: string;
        reviewFeedback: string;
        reviewer: {
          last_name: string;
          first_name: string;
        }
      }[];
    };
  },
  userId: User.selectors.userId(state),
  userForms: Review.selectors.userForms(state) as {
    id: string;
    // TODO: type this
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    contents: any;
    status: string
  }[]
});

export const connector = connect(mapStateToProps, mapDispatchToProps);
export type Props = ConnectedProps<typeof connector>;
