import { connect, ConnectedProps } from 'react-redux';
import { User, Review, Template } from '@ducks';
import { ColumnData } from '@engine/ducks/types';
import { get } from 'lodash';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mapStateToProps = (state: any) => ({
  checkedReviewRows: Review.selectors.checkedReviewRows(state),
  // TODO: type this properly
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  userForms: Review.selectors.userForms(state) as { id: string; formId: string; contents: any }[],
  orgCanSubmitToDOJ: User.selectors.canSubmitToDOJ(state),
  expandedRow: Review.selectors.expandedRow(state),
  userId: User.selectors.userId(state),
  hasEdit: Review.selectors.hasEdit(state),
  ReasonsForStop: get(Template.selectors.enumMap(state), 'ReasonsForStop.possibleValues', {}),
});

const mapDispatchToProps = {
  checkReviewRow: Review.checkReviewRow,
  setStopDescription: Review.setUserFormStopDescription,
  setSearchDescription: Review.setUserFormSearchDescription,
  approveReport: Review.approveReport,
  setReviewFeedbackDialogOpen: Review.setReviewFeedbackDialogOpen,
  setDojErrorsDialogOpen: Review.setDojErrorsDialogOpen,
  openReviewDialog: (formId: string) => Review.setReviewDialogOpen({ open: true, formId }),
  setExpandedRow: Review.setExpandedRow,
  addReviewTimeSpent: Review.addReviewTimeSpent,
  versionFormForReview: Review.versionFormForReview,
  setHasEdit: Review.setHasEdit,
};

export const connector = connect(mapStateToProps, mapDispatchToProps);
export type Props = ConnectedProps<typeof connector> & {
  colData: ColumnData[];
  // TODO: type this
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  rowData: any;
  rowIndex: number;
};
