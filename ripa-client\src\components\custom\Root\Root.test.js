import React from 'react'
import { render, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import createStore from '@engine/store'
import Root from './Root'
import DecoupledDispatchProvider from '../DecoupledDispatchProvider/DecoupledDispatchProvider'

jest.mock('@components/custom/Routes/Routes', () => () => <div data-testid="routes" />);

describe('Root component', () => {
  const store = createStore();
  it('renders a Routes in a Router', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <Root />
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Trigger the window resize event.
    global.dispatchEvent(new Event('resize'));

    expect(getByTestId('routes')).toBeInTheDocument();
    expect(getByTestId('root')).toBeInTheDocument();
  });

  it('breakpoint changes on window resize', async () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <Root />
        </DecoupledDispatchProvider>
      </Provider>
    );

    expect(getByTestId('root')).toHaveClass('lg', { exact: true });

    // Set a size that will be in the small breakpoint
    global.innerWidth = 500;

    // Trigger the window resize event.
    global.dispatchEvent(new Event('resize'));

    // Wait for resize event to update
    await waitFor(() => {
      expect(getByTestId('root')).toHaveClass('sm', { exact: true });
    },
      { timeout: 1000 });
  });
});
