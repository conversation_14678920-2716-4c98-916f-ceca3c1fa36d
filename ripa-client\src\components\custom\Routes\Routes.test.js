import React from 'react';
import { Role, RoleString } from '@ducks/constants';
import Routes from './Routes';
import { render, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import createStore from '@engine/store';
import { MemoryRouter } from 'react-router-dom';
import { assignRoute } from '@utility/assignRoute';
import { DecoupledDispatchProvider } from '@components/custom';
import * as User from '@ducks/user';
import * as Config from '@ducks/config';

jest.mock('@pages/NewReport/NewReport', () => () => <div data-testid="new-report" />);
jest.mock('@pages/Dashboard/Dashboard', () => () => <div data-testid="dashboard" />);
jest.mock('@pages/Login/Login', () => () => <div data-testid="login" />);
jest.mock('@pages/Admin/Users/<USER>', () => () => <div data-testid="users" />);
jest.mock('@pages/Review/Review', () => () => <div data-testid="review" />);
jest.mock('@pages/Visualization/Visualization', () => () => <div data-testid="visualization" />);

// Mock assigning route to check that it started the process
jest.mock('@utility/assignRoute', () => ({ assignRoute: jest.fn() }));

describe('Routes', () => {
  it('should redirect to dashboard on /', async () => {
    const store = createStore();

    store.dispatch(User.setAuthenticated('success'));
    store.dispatch(User.setUser({ roles: [RoleString[Role.Officer]] }))

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/']} initialIndex={0}>
            <Routes />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId('dashboard')).toBeInTheDocument();
    });
  });

  it('should redirect to /review/unavailable when on /review in mobile mode and only review role', async () => {
    const store = createStore();

    store.dispatch(User.setAuthenticated('success'));
    store.dispatch(Config.updatePageWidth(300));
    store.dispatch(User.setUser({ roles: [RoleString[Role.Reviewer]] }))

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/']} initialIndex={0}>
            <Routes />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId('review-unavailable')).toBeInTheDocument();
    });
  });


  it('should render Dashboard on /dashboard', async () => {
    const store = createStore();

    store.dispatch(User.setAuthenticated('success'));
    store.dispatch(User.setUser({ roles: [RoleString[Role.Officer]] }))

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/dashboard']} initialIndex={0}>
            <Routes />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId('dashboard')).toBeInTheDocument();
    });
  });
});

describe('Unauthenticated Routes', () => {
  it('should redirect to login on /', async () => {
    const store = createStore();

    store.dispatch(User.setAuthenticated('failure'))

    render(<Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/']} initialIndex={0}>
            <Routes />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(assignRoute).toBeCalledWith('/log_in');
    });
  });

  it('should render login on /log_in', async () => {
    const store = createStore();

    store.dispatch(User.setAuthenticated('failure'))

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/log_in']} initialIndex={0}>
            <Routes />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId('login')).toBeInTheDocument();
    });
  });
});

describe('Visualiation Route', () => {
  it('should render Visualization on /visualization', async() => {
    const store = createStore();

    store.dispatch(User.setAuthenticated('success'));
    store.dispatch(User.setUser({ roles: ['analyst'] }));

    const { getByTestId } = render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/visualization']} initialIndex={0}>
          <Routes />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId('visualization')).toBeInTheDocument();
    });
  });


  it('should not render Visualization on /visualization', async() => {
    const store = createStore();

    store.dispatch(User.setAuthenticated('success'));

    const { queryByTestId } = render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/visualization']} initialIndex={0}>
          <Routes />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(queryByTestId('visualization')).toBeNull();
    });
  });
});
