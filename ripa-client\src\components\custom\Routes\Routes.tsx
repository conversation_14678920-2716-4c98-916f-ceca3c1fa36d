import React from 'react';
import { matchPath } from 'react-router';
import { Switch } from 'react-router-dom';
import { AuthProvider } from '@components/custom';
import getPageComponent from './getPageComponent';
import { paths, unauthPaths } from './paths';

export const hasPath = ({ path, assignedRoles, isWorkingOffline, breakpoint, state }: {
  path: ValueOf<typeof paths> | ValueOf<typeof unauthPaths>;
  assignedRoles: string[];
  isWorkingOffline: boolean;
  breakpoint: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  state: any;
}) => {
  // @ts-expect-error TODO: Fix
  const offlineCondition = isWorkingOffline ? path.allowOffline : true;
  // @ts-expect-error TODO: Fix
  const hasRequiredRole = path?.requiredRole?.filter((r: string) => assignedRoles ? assignedRoles?.includes(r) : false)?.length > 0 ?? true;
  // @ts-expect-error TODO: Fix
  const hasAllowMobile = breakpoint === 'xs' ? (path?.allowMobile ?? true) : true;
  // @ts-expect-error TODO: Fix
  const hasRequiredState = state && path.requiredState ? path.requiredState.includes(state) : true;
  // @ts-expect-error TODO: Fix
  const hasExcludedRole = path?.excludedRole?.filter((r: string) => assignedRoles ? assignedRoles?.includes(r) : false)?.length > 0 ?? true;
  return !hasExcludedRole && offlineCondition && hasRequiredRole && hasAllowMobile && hasRequiredState;
};

export const isPath = (currentPath: string, path: string) => matchPath(currentPath, { path, exact: true, strict: false });

const props = { paths, unauthPaths, hasPath, isPath, getPageComponent }

const Routes = () => (
  <Switch>
    {/* These are top level routes that only link to pages. */}
    {/* @ts-expect-error TODO: Why doesn't AuthProvider have a construct or call signature? */}
    <AuthProvider {...props} />
  </Switch>
);

export default Routes;
