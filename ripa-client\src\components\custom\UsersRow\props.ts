import { connect, ConnectedProps } from 'react-redux';
import { User, Users } from '@ducks';
import { ColumnData } from '@engine/ducks/types';
import dayjs from 'dayjs';

const mapDispatchToProps = {
  checkUsersRow: Users.checkUsersRow,
  setEditUserDialogOpen: Users.setEditUserDialogOpen,
  setDisableUserDialogOpen: Users.setDisableUserDialogOpen,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mapStateToProps = (state: any) => ({
  userId: User.selectors.userId(state),
  checkedUsersRows: Users.selectors.checkedUsersRows(state),
});

export const connector = connect(mapStateToProps, mapDispatchToProps);
export type Props = ConnectedProps<typeof connector> & {
  colData: ColumnData[];
  rowData: {
    id: number;
    name: string;
    roles: string [];
    disabled: boolean;
    orgId: string;
    orgName: string;
    username: string;
    officerId: string;
    reviewers: {
      name: string;
    }[];
    created: dayjs.Dayjs;
  };
  rowIndex: number;
};
