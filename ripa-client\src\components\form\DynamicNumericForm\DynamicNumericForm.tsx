import React, { useEffect, useRef, useState } from 'react';
import { TextField } from '@material-ui/core';
import cn from 'classnames';
import { path } from 'ramda'
import { connect, ConnectedProps } from 'react-redux';
import * as Form from '@ducks/form';
import * as NewReport from '@ducks/newReport';
import { setValidationErrors } from '@ducks/form';
import { AlertLevel, createSnackNotification } from '@components/common';
import { onEnter } from '@utility/keypressHelpers';
import './DynamicNumericForm.scss';

let helperRef: React.RefObject<HTMLDivElement>;

interface Props extends PropsFromRedux {
  title: string;
  resultPath: string;
  max: number;
  min: number;
  counterToggle: boolean;
  goToNext: () => void;
  style?: React.CSSProperties;
  isAll: boolean;
  defaultValue: string;
}

const DynamicNumericForm = ({ title, resultPath, max, min, counterToggle, form, onChange,
  goToNext, style, currentPerson, labels, numberOfPeople, isAll, defaultValue
}: Props) => {
  const [number, setNumber] = useState<number>();
  const [reset, setReset] = useState<boolean>();
  helperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let n: number | null | undefined;
    if (isAll) {
      n = path(['custom', 'person', currentPerson, resultPath], form);
    } else {
      n = path(['custom', resultPath], form);
    }

    const defaultValueParsed = parseInt(defaultValue, 10);

    if (n === null || n === undefined) {
      n = !isNaN(defaultValueParsed) ? defaultValueParsed : counterToggle ? min : undefined;
    }

    setValueAndClearValidation(n);
    setReset(true)
  }, [resultPath, isAll, currentPerson, defaultValue, min, counterToggle]);

  useEffect(() => {
    if (reset) {
      setReset(false);
    }
  }, [reset]);

  const setValueAndClearValidation = (value?: number) => {
    value = value === undefined || isNaN(value) ? undefined : value;
    if (value === undefined || (value >= min && value <= max)) {
      if (isAll) {
        setNumber(value);
        // @ts-expect-error TODO: fix the types
        onChange(value, ['custom', 'person', currentPerson, resultPath]);
      } else {
        setNumber(value);
        onChange(value, ['custom', resultPath]);
      }
      setValidationErrors?.({});
      goToNext?.();
    } else {
      createSnackNotification(AlertLevel.Warning, 'Invalid input', `The input must be a number between ${min} and ${max}`);
    }
  }

  if (reset) {
    return <div />
  }

  return (
    <div
      className="dynamic-numeric-form"
      data-testid="dynamic-numeric-form"
      style={{ ...style, height: `calc(100% - ${helperRef.current?.clientHeight ?? 0}px)` }}
      key={`${resultPath}x${currentPerson}`}
    >
      {isAll && <div className="dynamic-numeric-form__person">
        <div className="material-icons">
          person
        </div>
        <div className="dynamic-numeric-form__person-label">
          {labels?.[currentPerson] || currentPerson + 1}
        </div>
        <div className="dynamic-numeric-form__person-same-for-all">
          Same For All
        </div>
        <div className="dynamic-numeric-form__person-progression">
          <b>{`${currentPerson + 1}`}</b>
          {`/${numberOfPeople}`}
        </div>
        <div className="dynamic-numeric-form__person-progression-all">
          ALL
        </div>
      </div>}
      <div className="dynamic-numeric-form__title">
        {title}
      </div>
      {counterToggle ?
        <div className="dynamic-numeric-form__counter-container">
          <div
            className="dynamic-numeric-form__counter"
            data-testid="dynamic-numeric-form-counter"
          >
            <div
              className={cn('dynamic-numeric-form__counter-minus material-icons', { enabled: number !== undefined && number > min })}
              data-testid="dynamic-numeric-form-counter-minus"
              onClick={() => number && number > min && setValueAndClearValidation(number - 1)}
              onKeyUp={() => number && number > min && onEnter(() => setValueAndClearValidation(number - 1))}
              aria-label="Decrease the number by one"
              tabIndex={0}
              role="button"
            >
              horizontal_rule
          </div>
            <div
              className="dynamic-numeric-form__counter-number"
              data-testid="dynamic-numeric-form-counter-number"
            >
              {number}
            </div>
            <div
              // TODO: Justify use of ! here?
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              className={cn('dynamic-numeric-form__counter-plus material-icons', { enabled: number! < max })}
              data-testid="dynamic-numeric-form-counter-plus"
              // TODO: Justify use of ! here?
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              onClick={() => number! < max ? setValueAndClearValidation(number! + 1) : null}
              // TODO: Justify use of ! here?
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              onKeyUp={() => number! < max ? onEnter(() => setValueAndClearValidation(number! + 1)) : null}
              aria-label="Increase the number by one"
              tabIndex={0}
              role="button"
            >
              add
          </div>
          </div>
        </div>
        :
        <div
          className="dynamic-numeric-form__number"
          data-testid="dynamic-numeric-form-number"
        >
          <TextField
            className="dynamic-numeric-form__number-control"
            variant="outlined"
            value={number}
            onChange={({ target: { value } }) => setValueAndClearValidation(parseInt(value, 10))}
            inputProps={{
              type: 'number',
              min,
              max,
              inputMode: 'numeric',
              'data-testid': 'dynamic-numeric-form-input',
            }}
          />
        </div>
      }
    </div>
  );
};

const Helper = ({ HelperTextInfo }: { HelperTextInfo?: string }) => {
  if (HelperTextInfo) {
    return (
      <div className="ripa-form-container__helper-box" ref={helperRef}>
        <div className="material-icons">
          help
        </div>
        <div className="ripa-form-container__helper-box-text">
          {HelperTextInfo}
        </div>
      </div>
    );
  }
  return null;
};

DynamicNumericForm.helper = Helper;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mapStateToProps = (state: any) => ({
  form: Form.selectors.form(state),
  labels: Form.selectors.labels(state),
  numberOfPeople: Form.selectors.numberOfPeople(state),
  currentPerson: NewReport.selectors.currentSubloop(state)
});

const mapDispatchToProps = {
  onChange: (value: unknown, resultPath: string[]) => Form.setFormField({ path: resultPath })(value)
};

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(DynamicNumericForm)
