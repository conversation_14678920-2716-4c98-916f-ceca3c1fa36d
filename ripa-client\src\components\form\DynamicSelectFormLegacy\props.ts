import { connect, ConnectedProps } from 'react-redux';
import { Form } from '@ducks';
import React from 'react';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mapStateToProps = (state: any) => ({
  form: Form.selectors.form(state),
});

const mapDispatchToProps = {
  onChange: (value: string | string[], resultPath: (string | number)[]) => Form.setFormField({ path: resultPath })(value),
};

export const connector = connect(mapStateToProps, mapDispatchToProps);
export type Props = ConnectedProps<typeof connector> & {
  title: string;
  skipText: string;
  options: { value: string }[];
  skip: boolean;
  onSkipChange?: (param: boolean) => void;
  setValidationErrors?: (param: unknown) => void;
  useSkip: boolean;
  resultPath: string[];
  selectBoxStyle?: React.CSSProperties;
  DynamicSelectFormStyle?: React.CSSProperties;
  goToNext?: () => void;
};
