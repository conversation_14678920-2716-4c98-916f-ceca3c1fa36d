import React, { useState, useEffect } from 'react'
import { connect, ConnectedProps } from 'react-redux'
import { TextField, CircularProgress } from '@material-ui/core'
import Autocomplete from '@material-ui/lab/Autocomplete'
import * as Form from '@ducks/form';
import sleep from '@utility/sleep'
import './RipaSchoolForm.scss'
import { School } from '@engine/ducks/types';

const initSchools: School[] = [{
  CDSCode: '',
  StatusType: '',
  County: '',
  District: '',
  School: '',
  Street: '',
  StreetAbr: '',
  City: '',
  Zip: '',
  State: ''
}]

const RipaSchoolForm = ({ setSchool, school, errors, checkValidation }: PropsFromRedux) => {
  const [open, setOpen] = useState(false)
  const [options, setOptions] = useState<School[]>(initSchools)
  const loading = open && options.length === 0

  async function loadingSetOptions() {
    if (!loading) {
      return undefined
    }
    await sleep(1000)
    setOptions(window.schools || []);
  }

  useEffect(() => {
    loadingSetOptions();
  }, [loading])
  useEffect(() => {
    if (!open) {
      setOptions([])
    }
  }, [open])

  return <div className="ripa-school-form" data-testid="ripa-school-form">
    <div className="ripa-school-form__title">
      What was the name of the school?
    </div>
    <Autocomplete
      className="ripa-school-form__autocomplete"
      data-testid="ripa-school-form-autocomplete"
      open={open}
      defaultValue={window?.schools?.find((s) => s.School.localeCompare(school || '', 'en', { sensitivity: 'base' }) === 0)}
      onOpen={() => setOpen(true)}
      onClose={() => setOpen(false)}
      onInputChange={(_, v) => setSchool(v)}
      getOptionLabel={option => option.School}
      options={options}
      loading={loading}
      freeSolo
      onBlur={() => checkValidation()}
      renderInput={params => <TextField
        {...params}
        error={errors?.school ? errors.school > 0 : false}
        placeholder="Lincoln Elementary School..."
        variant="outlined"
        InputProps={{
          ...params.InputProps,
          endAdornment: <>
            {loading ? <CircularProgress color="inherit" size={20} /> : null}
            {params.InputProps.endAdornment}
          </>
        }}
        inputProps={{
          ...params.inputProps,
          'data-testid': 'ripa-school-form-autocomplete-input'
        }}
      />}
    />
  </div>
}

const Helper = () => (
  <div className="ripa-form-container__helper-box center">
    <div className="material-icons">
      help
    </div>
    <div className="ripa-form-container__helper-box-text">
      If a person stopped is a student at a K-12 public school, report the school name.
    </div>
  </div>
)

RipaSchoolForm.helper = Helper

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mapStateToProps = (state: any) => ({
  school: Form.selectors.school(state),
  errors: Form.selectors.stepErrors(state),
})

const mapDispatchToProps = {
  setSchool: Form.setFormField<string>({ path: ['school'] }),
  checkValidation: Form.checkValidation,
}

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(RipaSchoolForm);
