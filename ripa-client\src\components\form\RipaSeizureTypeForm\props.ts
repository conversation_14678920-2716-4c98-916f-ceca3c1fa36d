import { connect, ConnectedProps } from 'react-redux';
import { Template, Form, NewReport } from '@ducks';
import { get } from 'lodash';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mapStateToProps = (state: any) => ({
  seizedPropertyType: Form.selectors.seizedPropertyType(state),
  currentSubloop: NewReport.selectors.currentSubloop(state),
  labels: Form.selectors.labels(state),
  numberOfPeople: Form.selectors.numberOfPeople(state),
  useSameActionTakenForAll: Form.selectors.useSameActionTakenForAll(state),
  useSameActionForAllPersonIndex: Form.selectors.useSameActionForAllPersonIndex(state),
  // TODO: Fix inference types
  typeSeized: get(Template.selectors.enumMap(state), 'typeOfPropertySeized.possibleValues', {}) as Record<string, { value: string}>,
});

const mapDispatchToProps = {
  toggleSeizedPropertyType: (value: string, personIndex: number) => Form.toggleSeizedPropertyType({ value, personIndex }),
};

export const connector = connect(mapStateToProps, mapDispatchToProps);
export type Props = ConnectedProps<typeof connector>;
