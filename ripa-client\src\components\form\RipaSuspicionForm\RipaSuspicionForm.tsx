import React from 'react';
import cn from 'classnames';
import { TextField, Tooltip } from '@material-ui/core';
import { get, xor } from 'lodash';
import Autocomplete, { createFilterOptions } from '@material-ui/lab/Autocomplete';
import { onEnter } from '@utility/keypressHelpers';
import offenseCode from '@utility/offenseCode';
import dayjs from 'dayjs';
import { Props, connector } from './props';
import './RipaSuspicionForm.scss';

type Offense = ReturnType<typeof offenseCode>[0];

const RipaSuspicionForm = ({
  errors,
  setReasonableSuspicion,
  setReasonableSuspicionCode,
  reasonableSuspicion,
  reasonableSuspicionCode,
  ReasonableSuspicion,
  labels,
  numberOfPeople,
  useSamePrimaryReasonForAll,
  currentSubloop,
  flags: { pre2024 },
  clipboardCodeFeature
}: Props) => {
  const subloopIndex = useSamePrimaryReasonForAll ? 0 : currentSubloop;
  const matchedSuspectVehicleValue = pre2024 && ReasonableSuspicion?.MatchedDescriptionSuspectVehicle?.value;
  const reasonableSuspicionsForPerson = Array.isArray(reasonableSuspicion[subloopIndex]) ? reasonableSuspicion[subloopIndex] : [reasonableSuspicion[subloopIndex]];
  const filterOptions = createFilterOptions<Offense>({
    matchFrom: 'any',
    limit: 50,
  });
  const copyAndSetReasonableSuspicionCode = (value: string, index: number) => {
    if (clipboardCodeFeature) {
      try {
        navigator.clipboard.writeText(value).catch((error) => {
          // Clipboard access denied by user;
          if (error instanceof DOMException && error.name === 'NotAllowedError') { return; }
          throw (error);
        });
      } catch {
        // No harm
      }
    }
    setReasonableSuspicionCode(value, index);
  }
  return (
    <div className="ripa-suspicion-form" data-testid="ripa-suspicion-form">
      <div className={cn('ripa-suspicion-form__person', { 'same-for-all': useSamePrimaryReasonForAll })}>
        <div className="material-icons">person</div>
        <div className="ripa-suspicion-form__person-label">{labels?.[subloopIndex] || subloopIndex + 1}</div>
        <div className="ripa-suspicion-form__person-same-for-all">Same For All</div>
        <div className="ripa-suspicion-form__person-progression">
          <b>{`${subloopIndex + 1}`}</b>
          {`/${numberOfPeople}`}
        </div>
        <div className="ripa-suspicion-form__person-progression-all">ALL</div>
      </div>
      <div className="ripa-suspicion-form__title">What criminal activity had suspicion?</div>
      <div className="ripa-suspicion-form__select-box-container">
        <div className="ripa-suspicion-form__select-box-content">
          {[
            ReasonableSuspicion.WitnessedCommission,
            ReasonableSuspicion.MatchedDescription,
            ReasonableSuspicion.WitnessVictimId,
            ReasonableSuspicion.CarryingSuspiciousObject,
            ReasonableSuspicion.ActionsIndicativeCasingVictimLocation,
            ReasonableSuspicion.ActingAsLookout,
            ReasonableSuspicion.IndicativeOfDrugTransaction,
            ReasonableSuspicion.IndicativeOfEngagingViolentCrime,
            ReasonableSuspicion.Other,
          ].map(({ value }, vi) => (
            <div
              className={cn('ripa-suspicion-form__select-box', {
                active: reasonableSuspicionsForPerson?.includes(value),
              })}
              key={`RipaSuspicionFormBox-${vi}}`}
              onClick={() => setReasonableSuspicion(xor(reasonableSuspicionsForPerson, [value]), subloopIndex)}
              onKeyUp={onEnter(() => setReasonableSuspicion(xor(reasonableSuspicionsForPerson, [value]), subloopIndex))}
              data-testid={`ripa-suspicion-form-box-${vi}`}
              role="button"
              tabIndex={0}
            >
              {value}
            </div>
          ))}
          {pre2024 && (
            <div
              className={cn('ripa-suspicion-form__select-box', {
                active: reasonableSuspicionsForPerson?.includes(matchedSuspectVehicleValue),
              })}
              onClick={() => setReasonableSuspicion(xor(reasonableSuspicionsForPerson, [matchedSuspectVehicleValue]), subloopIndex)}
              onKeyUp={onEnter(() => setReasonableSuspicion(xor(reasonableSuspicionsForPerson, [matchedSuspectVehicleValue]), subloopIndex))}
              data-testid="ripa-suspicion-form-box-MatchedDescriptionSuspectVehicle"
              role="button"
              tabIndex={0}
            >
              {matchedSuspectVehicleValue}
            </div>
          )}
          <Autocomplete<Offense>
            autoSelect
            autoHighlight
            className="ripa-suspicion-form__autocomplete"
            classes={{ option: 'code' }}
            data-testid="ripa-suspicion-form-autocomplete"
            onInputChange={(_, v) => copyAndSetReasonableSuspicionCode(v, subloopIndex)}
            blurOnSelect={true}
            defaultValue={offenseCode(dayjs()).find((v) => v.label === reasonableSuspicionCode[subloopIndex])}
            getOptionLabel={(o) => o.label}
            renderOption={(o) => (
              <>
                {o.Repealed !== '' ? (
                  <Tooltip title={`Offense Code Repealed ${o.Repealed}`}>
                    { /* @ts-expect-error TODO: fix type for legacy forms */ }
                    <span data-type={o.TypeOfCharge ?? o.OffenseTypeofCharge} style={{ textDecoration: 'line-through' }}>
                      {o.label}
                      {o.TypeOfStatute ? ` (${o.TypeOfStatute})` : ''}
                    </span>
                  </Tooltip>
                ) : (
                  // @ts-expect-error TODO: fix type for legacy forms
                  <span data-type={o.TypeOfCharge ?? o.OffenseTypeofCharge}>
                    {o.label}
                    {o.TypeOfStatute ? ` (${o.TypeOfStatute})` : ''}
                  </span>
                )}
              </>
            )}
            options={offenseCode(dayjs())}
            filterOptions={filterOptions}
            renderInput={(params) => (
              <TextField
                {...params}
                error={get(errors, 'reasonableSuspicionCode', 0) > 0}
                placeholder="If known, Code for suspected violation..."
                variant="outlined"
                inputProps={{
                  ...params.inputProps,
                  'data-testid': 'ripa-suspicion-form-autocomplete-input',
                }}
              />
            )}
          />
        </div>
      </div>
    </div>
  );
};

const Helper = () => (
  <div className="ripa-form-container__helper-box center">
    <div className="material-icons">help</div>
    <div className="ripa-form-container__helper-box-text">Select all that apply to describe the basis of suspicion.</div>
  </div>
);

RipaSuspicionForm.helper = Helper;

export default connector(RipaSuspicionForm);
