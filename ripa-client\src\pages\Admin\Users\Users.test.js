/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/prop-types */
import { DecoupledDispatchProvider } from '@components/custom';
import * as Config from '@ducks/config';
import createStore from '@engine/store';
import { fireEvent, render, waitFor } from '@testing-library/react';
import { dispatchCustomEvent } from '@utility/customEvent';
import sleep from '@utility/sleep';
import { request } from 'axios';
import dayjs from 'dayjs';
import qs from 'qs';
import React from 'react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import Users from './Users';

jest.mock('axios', () => ({ request: jest.fn() }));
jest.mock('@utility/customEvent', () => ({ dispatchCustomEvent: jest.fn() }));
jest.mock('nanoid', () => ({ customAlphabet: () => () => '123456789' }));
jest.mock('@material-ui/core/Select', () => (props) => (
  <>
    <input
      data-testid={props['data-testid']}
      onChange={({ target: { value } }) => {
        if (props['data-testid'].includes('reviewer')) {
          value = value.split(',');
          props.onChange({ target: { value } });
        } else {
          props.onChange({ target: { value } });
        }
      }}
    />
    {props.renderValue && <div data-testid={`${props['data-testid']}-render-value`}>{props.renderValue(props.value)}</div>}
  </>
));

/* Tests may take a while */
jest.setTimeout(10000);

const usersMock = {
  users: [{
    id: '11',
    email: '<EMAIL>',
    created_at: '2021-04-07T18:21:49.999Z',
    updated_at: '2021-04-07T18:23:35.928Z',
    organization_id: 2,
    username: 'dan7554',
    first_name: 'daniel',
    last_name: 'christiani',
    roles: ['officer', 'reviewer', 'admin'],
    officer_id: 'W7YDX33C2',
    deleted_at: null,
    years_of_experience: 11,
    reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
      id: '10',
      first_name: 'testest',
      last_name: 'testestt',
    }],
  }, {
    id: '13',
    email: '<EMAIL>',
    created_at: '2021-04-07T18:24:24.959Z',
    updated_at: '2021-04-07T18:24:24.959Z',
    organization_id: 2,
    username: 'dawdadwd',
    first_name: 'www',
    last_name: 'ggg',
    roles: ['officer', 'admin'],
    officer_id: 'Q13UL63A0',
    deleted_at: null,
    years_of_experience: 2,
    reviewers: [{ id: '11', first_name: 'daniel', last_name: 'christiani' }, {
      id: '5',
      first_name: 'Safiya',
      last_name: 'Flowers',
    }, { id: '10', first_name: 'testest', last_name: 'testestt' }],
  }, {
    id: '12',
    email: '<EMAIL>',
    created_at: '2021-04-07T18:23:54.169Z',
    updated_at: '2021-04-07T18:23:54.169Z',
    organization_id: 2,
    username: 'ddd',
    first_name: 'w',
    last_name: 'd',
    roles: ['officer'],
    officer_id: 'Y1KDPZV3T',
    deleted_at: null,
    years_of_experience: 2,
    reviewers: [],
  }, {
    id: '8',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.664Z',
    updated_at: '2021-04-07T18:09:38.748Z',
    organization_id: 2,
    username: 'demo_admin_1',
    first_name: 'Parker',
    last_name: 'Tillus',
    roles: ['officer', 'admin'],
    officer_id: '10WESTCCI',
    deleted_at: null,
    years_of_experience: 55,
    reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
      id: '10',
      first_name: 'testest',
      last_name: 'testestt',
    }],
  }, {
    id: '6',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.568Z',
    updated_at: '2021-04-05T21:41:16.568Z',
    organization_id: 2,
    username: 'demo_officer_6',
    first_name: 'Sophie',
    last_name: 'Lola',
    roles: ['officer'],
    officer_id: 'LLOKPIHU8',
    deleted_at: null,
    years_of_experience: 3,
    reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
      id: '10',
      first_name: 'testest',
      last_name: 'testestt',
    }],
  }, {
    id: '7',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.568Z',
    updated_at: '2021-04-05T21:41:16.568Z',
    organization_id: 2,
    username: 'demo_officer_7',
    first_name: 'Jamie',
    last_name: 'Gadner',
    roles: ['officer'],
    officer_id: 'OJUIY897L',
    deleted_at: null,
    years_of_experience: 5,
    reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
      id: '10',
      first_name: 'testest',
      last_name: 'testestt',
    }],
  }, {
    id: '9',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.568Z',
    updated_at: '2021-04-05T21:41:16.568Z',
    organization_id: 2,
    username: 'demo_officer_9',
    first_name: 'Nine',
    last_name: 'Miler',
    roles: ['officer'],
    officer_id: 'OJ55Y897L',
    deleted_at: null,
    years_of_experience: 5,
    reviewers: [],
  }, {
    id: '1',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.521Z',
    updated_at: '2021-04-07T18:08:32.709Z',
    organization_id: 2,
    username: 'demo_officer_1',
    first_name: 'Edison',
    last_name: 'Parks',
    roles: ['officer'],
    officer_id: 'TTDJWMFRY',
    deleted_at: null,
    years_of_experience: 19,
    reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }],
  }, {
    id: '2',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.549Z',
    updated_at: '2021-04-05T21:41:16.549Z',
    organization_id: 2,
    username: 'demo_officer_2',
    first_name: 'Morgan',
    last_name: 'Bate',
    roles: ['officer'],
    officer_id: 'ripa-1234-demo-UUID-2',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }],
  }, {
    id: '3',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.568Z',
    updated_at: '2021-04-05T21:41:16.568Z',
    organization_id: 2,
    username: 'demo_officer_3',
    first_name: 'Nannie',
    last_name: 'Edge',
    roles: ['officer'],
    officer_id: 'ripa-1234-demo-UUID-3',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [],
  }, {
    id: '4',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.586Z',
    updated_at: '2021-04-05T21:41:16.586Z',
    organization_id: 2,
    username: 'demo_officer_4',
    first_name: 'Igor',
    last_name: 'Burns',
    roles: ['officer'],
    officer_id: 'ripa-1234-demo-UUID-4',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [],
  }, {
    id: '14',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.586Z',
    updated_at: '2021-04-05T21:41:16.586Z',
    organization_id: 2,
    username: 'demo_officer_14',
    first_name: 'Lily',
    last_name: 'Lily',
    roles: ['officer'],
    officer_id: 'LILY94032',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [],
  }, {
    id: '15',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.586Z',
    updated_at: '2021-04-05T21:41:16.586Z',
    organization_id: 2,
    username: 'demo_officer_15',
    first_name: 'Ted',
    last_name: 'Mosby',
    roles: ['officer'],
    officer_id: 'THEMOSBY01',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [],
  }, {
    id: '16',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.586Z',
    updated_at: '2021-04-05T21:41:16.586Z',
    organization_id: 2,
    username: 'demo_officer_16',
    first_name: 'Barney',
    last_name: 'Stinson',
    roles: ['officer'],
    officer_id: 'CHIPTRI01',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [],
  }, {
    id: '5',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.605Z',
    updated_at: '2021-04-05T21:41:16.605Z',
    organization_id: 2,
    username: 'demo_reviewer_1',
    first_name: 'Safiya',
    last_name: 'Flowers',
    roles: ['officer', 'reviewer'],
    officer_id: 'ripa-1234-demo-UUID-5',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [],
  }, {
    id: '10',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:51:02.421Z',
    updated_at: '2021-04-07T18:09:22.569Z',
    organization_id: 2,
    username: 'testestest',
    first_name: 'testest',
    last_name: 'testestt',
    roles: ['officer', 'reviewer', 'admin'],
    officer_id: 'TYCJ1DMM7',
    deleted_at: null,
    years_of_experience: 3,
    reviewers: [],
  }
  ],
  pagination: { count: 16, page: 1, items: 10, pages: 2 },
};

const orgsMock = {
  pagination: {
    pages: 1,
    count: 4,
  },
  organizations: [
    {
      id: '0',
      created_at: dayjs().format('MM/DD/YYYY'),
      name: 'organization 0',
      subdomain: 'domain0',
    },
    {
      id: '1',
      created_at: dayjs().format('MM/DD/YYYY'),
      name: 'organization 1',
      subdomain: 'domain1',
    },
    {
      id: '2',
      created_at: dayjs().format('MM/DD/YYYY'),
      name: 'organization 2',
      subdomain: 'domain2',
    },
    {
      id: '3',
      created_at: dayjs().format('MM/DD/YYYY'),
      name: 'organization 3',
      subdomain: 'domain3',
    },
  ],
};

const reviewersMock = {
  users: [{
    id: '10',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:51:02.421Z',
    updated_at: '2021-04-07T18:09:22.569Z',
    organization_id: 2,
    username: 'testestest',
    first_name: 'testest',
    last_name: 'testestt',
    roles: ['officer', 'reviewer', 'admin'],
    officer_id: 'TYCJ1DMM7',
    deleted_at: null,
    years_of_experience: 3,
    reviewers: [],
  }, {
    id: '5',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.605Z',
    updated_at: '2021-04-05T21:41:16.605Z',
    organization_id: 2,
    username: 'demo_reviewer_1',
    first_name: 'Safiya',
    last_name: 'Flowers',
    roles: ['officer', 'reviewer'],
    officer_id: 'ripa-1234-demo-UUID-5',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [],
  }, {
    id: '11',
    email: '<EMAIL>',
    created_at: '2021-04-07T18:21:49.999Z',
    updated_at: '2021-04-07T18:23:35.928Z',
    organization_id: 2,
    username: 'dan7554',
    first_name: 'daniel',
    last_name: 'christiani',
    roles: ['officer', 'reviewer', 'admin'],
    officer_id: 'W7YDX33C2',
    deleted_at: null,
    years_of_experience: 11,
    reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
      id: '10',
      first_name: 'testest',
      last_name: 'testestt',
    }],
  }], pagination: { count: 3, page: 1, items: 3, pages: 1 },
};

const revieweesMock = {
  reviewerships: [{
    id: '10',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:51:02.421Z',
    updated_at: '2021-04-07T18:09:22.569Z',
    organization_id: 2,
    username: 'testestest',
    first_name: 'testest',
    last_name: 'testestt',
    roles: ['officer', 'reviewer', 'admin'],
    officer_id: 'TYCJ1DMM7',
    deleted_at: null,
    years_of_experience: 3,
    reviewers: [],
  }, {
    id: '5',
    email: '<EMAIL>',
    created_at: '2021-04-05T21:41:16.605Z',
    updated_at: '2021-04-05T21:41:16.605Z',
    organization_id: 2,
    username: 'demo_reviewer_1',
    first_name: 'Safiya',
    last_name: 'Flowers',
    roles: ['officer', 'reviewer'],
    officer_id: 'ripa-1234-demo-UUID-5',
    deleted_at: null,
    years_of_experience: 1,
    reviewers: [],
  }, {
    id: '11',
    email: '<EMAIL>',
    created_at: '2021-04-07T18:21:49.999Z',
    updated_at: '2021-04-07T18:23:35.928Z',
    organization_id: 2,
    username: 'dan7554',
    first_name: 'daniel',
    last_name: 'christiani',
    roles: ['officer', 'reviewer', 'admin'],
    officer_id: 'W7YDX33C2',
    deleted_at: null,
    years_of_experience: 11,
    reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
      id: '10',
      first_name: 'testest',
      last_name: 'testestt',
    }],
  }], pagination: { count: 3, page: 1, items: 3, pages: 1 },
};

const usersApiMocks = ({ url, method }) => {
  // Page load get all users call
  if (url.includes('users') && method === 'get' && !url.includes('limit=300&role=reviewer')) {
    return Promise.resolve({ data: usersMock });
  }
  // Page load Get all reviewers call
  if (url.includes('users') && method === 'get' && url.includes('limit=300&role=reviewer')) {
    return Promise.resolve({ data: reviewersMock });
  }
  // Add user
  if (url.includes('users') && method === 'post') {
    return Promise.resolve({ data: { id: 1234 } });
  }
  // Edit user
  if (url.includes('users') && method === 'patch') {
    return Promise.resolve({ data: { id: 4321 } });
  }
  // Add reviewers to user
  if (url.includes('reviewerships_by_writer') && method === 'post') {
    return Promise.resolve();
  }

  // Add reviewership to user
  if (url.includes('reviewerships') && url.includes('limit=300&reviewer_id')) {
    return Promise.resolve({ data: revieweesMock });
  }
  // Page load get latest template
  if (url.includes('form_templates') && method === 'get') {
    return Promise.resolve({
      data: [
        {
          answers: window.testEnums.answers,
          form_template_id: window.testEnums.form_template_id,
          name: window.testEnums.name,
          version: window.testEnums.version,
        },
      ],
    })
  }
  if (url.includes('organizations')) {
    return Promise.resolve({ data: orgsMock });
  }
};

describe('Users page', () => {
  it('renders', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url }) => {
      if (url.includes('users')) {
        return Promise.resolve({ data: usersMock });
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    expect(getByTestId('users')).toBeInTheDocument();
  });

  it('searches using middleware after the search field changes', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url }) => {
      if (url.includes('users')) {
        return Promise.resolve({ data: usersMock });
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    const { getByTestId, queryByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Change search field
    fireEvent.change(getByTestId('users-search'), { target: { value: 'asds' } });

    // First row is showing
    await waitFor(() => {
      expect(queryByTestId('users-row-cell-0-11-0')).toBeNull();
    });

    // Change search field
    fireEvent.change(getByTestId('users-search'), { target: { value: '' } });

    // First row is showing
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

  });

  it('filter with role', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url }) => {
      if (url.includes('users')) {
        return Promise.resolve({ data: usersMock });
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );
    await sleep(200);
    // Change filter role field
    fireEvent.change(getByTestId('users-dropdown-roles'), { target: { value: '4' } });

    request.mockImplementation(({ method }) => {
      if (method === 'get') {
        return Promise.resolve({
          data: {
            pagination: {
              pages: 1,
              count: 1,
            },
            users: [{
              id: '14',
              email: '<EMAIL>',
              created_at: '2021-04-05T21:51:02.421Z',
              updated_at: '2021-04-07T18:09:22.569Z',
              organization_id: 2,
              username: 'linhdv',
              first_name: 'linh',
              last_name: 'dv',
              roles: ['analyst'],
              officer_id: 'TYCJ1DMM7',
              deleted_at: null,
              years_of_experience: 3,
              reviewers: [],
            }],
          },
        });
      }
      return Promise.resolve({ data: {} })
    });

    await sleep(100);

    // First row is showing
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-14-0')).toBeInTheDocument();
    });

  });

  it('shows correct pagination when filtering', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url }) => {
      if (url.includes('users')) {
        return Promise.resolve({ data: usersMock });
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );
    await sleep(500);

    await waitFor(() => {
      expect(getByTestId('table-pagination-page-selector-text')).toContainHTML(
        `<div class="table__pagination-page-selector-text" data-testid="table-pagination-page-selector-text"><b>1 - 10 </b> of 16</div>`);
    });

    // Navigate to the next page
    fireEvent.click(getByTestId('table-pagination-page-selector-next'));

    await waitFor(() => {
      expect(getByTestId('table-pagination-page-selector-text')).toContainHTML(
        '<div class="table__pagination-page-selector-text" data-testid="table-pagination-page-selector-text"><b>11 - 16 </b> of 16</div>'
      );
    });

    fireEvent.change(getByTestId('filter-by-reviewer'), {
      target: { value: '10' },
    });

    request.mockImplementation(({ method }) => {
      if (method === 'get') {
        return Promise.resolve({
          data: {
            pagination: {
              pages: 1,
              count: 5,
            },
            users: [{
              id: '11',
              email: '<EMAIL>',
              created_at: '2021-04-07T18:21:49.999Z',
              updated_at: '2021-04-07T18:23:35.928Z',
              organization_id: 2,
              username: 'dan7554',
              first_name: 'daniel',
              last_name: 'christiani',
              roles: ['officer', 'reviewer', 'admin'],
              officer_id: 'W7YDX33C2',
              deleted_at: null,
              years_of_experience: 11,
              reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
                id: '10',
                first_name: 'testest',
                last_name: 'testestt',
              }],
            }, {
              id: '13',
              email: '<EMAIL>',
              created_at: '2021-04-07T18:24:24.959Z',
              updated_at: '2021-04-07T18:24:24.959Z',
              organization_id: 2,
              username: 'dawdadwd',
              first_name: 'www',
              last_name: 'ggg',
              roles: ['officer', 'admin'],
              officer_id: 'Q13UL63A0',
              deleted_at: null,
              years_of_experience: 2,
              reviewers: [{ id: '11', first_name: 'daniel', last_name: 'christiani' }, {
                id: '5',
                first_name: 'Safiya',
                last_name: 'Flowers',
              }, { id: '10', first_name: 'testest', last_name: 'testestt' }],
            }, {
              id: '8',
              email: '<EMAIL>',
              created_at: '2021-04-05T21:41:16.664Z',
              updated_at: '2021-04-07T18:09:38.748Z',
              organization_id: 2,
              username: 'demo_admin_1',
              first_name: 'Parker',
              last_name: 'Tillus',
              roles: ['officer', 'admin'],
              officer_id: '10WESTCCI',
              deleted_at: null,
              years_of_experience: 55,
              reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
                id: '10',
                first_name: 'testest',
                last_name: 'testestt',
              }],
            }, {
              id: '6',
              email: '<EMAIL>',
              created_at: '2021-04-05T21:41:16.568Z',
              updated_at: '2021-04-05T21:41:16.568Z',
              organization_id: 2,
              username: 'demo_officer_6',
              first_name: 'Sophie',
              last_name: 'Lola',
              roles: ['officer'],
              officer_id: 'LLOKPIHU8',
              deleted_at: null,
              years_of_experience: 3,
              reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
                id: '10',
                first_name: 'testest',
                last_name: 'testestt',
              }],
            }, {
              id: '7',
              email: '<EMAIL>',
              created_at: '2021-04-05T21:41:16.568Z',
              updated_at: '2021-04-05T21:41:16.568Z',
              organization_id: 2,
              username: 'demo_officer_7',
              first_name: 'Jamie',
              last_name: 'Gadner',
              roles: ['officer'],
              officer_id: 'OJUIY897L',
              deleted_at: null,
              years_of_experience: 5,
              reviewers: [{ id: '5', first_name: 'Safiya', last_name: 'Flowers' }, {
                id: '10',
                first_name: 'testest',
                last_name: 'testestt',
              }],
            }],
          },
        });
      }
      return Promise.resolve();
    });

    await waitFor(() => {
      expect(getByTestId('table-pagination-page-selector-text')).toContainHTML(
        '<div class="table__pagination-page-selector-text" data-testid="table-pagination-page-selector-text"><b>1 - 5 </b> of 5</div>'
      );
    })

  });

  it('can assign reviewer', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url }) => {
      if (url.includes('users')) {
        return Promise.resolve({ data: usersMock });
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    const { getByTestId, getAllByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );
    await sleep(2000);
    // Change reviewer field
    fireEvent.change(getByTestId('users-dropdown-assign-reviewer'), { target: { value: ['11', '12'] } });

    // select officer field
    fireEvent.click(getAllByTestId('users-row-cell-select-checkbox')[2]);

    // submit assign
    fireEvent.click(getByTestId('users-change-assignment-button'));

    // user is checked
    await waitFor(() => {
      expect(getAllByTestId('users-row-cell-select-checkbox')[2]).toHaveClass('checked')
    })

    // Submit snack created
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith(
        'create-snack-notification',
        {
          'body': 'Reviewerships updated',
          'level': { 'icon': 'alert-success.svg', 'type': 'success' },
          'title': 'Success',
        });
    }, { timeout: 4000 });
  });

  it('adds a user', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/dashboard']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Lets reset mocks so initial requests will not be recorded
    request.mockClear();

    // Click add user button
    fireEvent.click(getByTestId('users-add-user'));

    // Add user dialog appears
    await waitFor(() => {
      expect(getByTestId('add-user'));
    });

    fireEvent.change(getByTestId('add-user-name-first-name'), { target: { value: 'woofwoof' } });
    fireEvent.change(getByTestId('add-user-name-last-name'), { target: { value: 'man' } });
    fireEvent.change(getByTestId('add-user-email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(getByTestId('add-user-username'), { target: { value: 'wwman' } });
    fireEvent.change(getByTestId('add-user-officer-id'), { target: { value: '123456789' } });
    fireEvent.change(getByTestId('add-user-years'), { target: { value: '3' } });
    fireEvent.change(getByTestId('add-user-dropdown-reviewer'), { target: { value: '10' } });
    fireEvent.change(getByTestId('add-user-password'), { target: { value: '@Eqan1097552' } });
    fireEvent.change(getByTestId('add-user-confirm-password'), { target: { value: '@Eqan1097552' } });

    // Submit new user
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Submit snack created
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith(
        'create-snack-notification',
        {
          'body': 'User created </br> <small>1234-123456789</small>',
          'level': { 'icon': 'alert-success.svg', 'type': 'success' },
          'title': 'Success',
        });
    });

    // Reviewers are created
    await waitFor(() => {
      expect(request).toBeCalledWith({
        url: 'http://localhost/api/v1/reviewerships_by_writer',
        method: 'post',
        data: {
          reviewer_ids: ['10'],
          writer_id: 1234,
        },
        maxRedirects: 0,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      });
    });
  });

  it('does not add a user with a duplicate email', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url, method }) => {
      // Page load get all users call
      if (url.includes('users') && method === 'get' && !url.includes('limit=300&role=reviewer')) {
        return Promise.resolve({ data: usersMock });
      }
      // Add user
      if (url.includes('users') && method === 'post') {
        return Promise.reject({ response: { data: { error: { message: 'Invalid create parameters' }, errors: { email: 'has already been taken' } } } });
      }
      // Page load get latest template
      if (url.includes('form_templates') && method === 'get') {
        return Promise.resolve({
          data: [
            {
              answers: window.testEnums.answers,
              form_template_id: window.testEnums.form_template_id,
              name: window.testEnums.name,
              version: window.testEnums.version,
            },
          ],
        })
      }

      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Click add user button
    fireEvent.click(getByTestId('users-add-user'));

    // Add user dialog appears
    await waitFor(() => {
      expect(getByTestId('add-user'));
    });

    fireEvent.change(getByTestId('add-user-name-first-name'), { target: { value: 'First' } });
    fireEvent.change(getByTestId('add-user-name-last-name'), { target: { value: 'Last' } });
    fireEvent.change(getByTestId('add-user-email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(getByTestId('add-user-username'), { target: { value: 'username' } });
    fireEvent.change(getByTestId('add-user-officer-id'), { target: { value: '987654321' } });
    fireEvent.change(getByTestId('add-user-years'), { target: { value: '12' } });
    fireEvent.change(getByTestId('add-user-dropdown-reviewer'), { target: { value: '10' } });
    fireEvent.change(getByTestId('add-user-password'), { target: { value: 'Ripa_demo1' } });
    fireEvent.change(getByTestId('add-user-confirm-password'), { target: { value: 'Ripa_demo1' } });

    // Try and submit new user
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Submit snack created
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith(
        'create-snack-notification',
        {
          'body': 'Invalid create parameters',
          'level': { 'icon': 'alert-error.svg', 'type': 'error' },
          'title': 'Failed to create user',
        });
    });

    // Add user error message appears
    await waitFor(() => {
      expect(getByTestId('label-field-error'));
    });
  });

  it('does not add a user with a duplicate username', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url, method }) => {
      // Page load get all users call
      if (url.includes('users') && method === 'get' && !url.includes('limit=300&role=reviewer')) {
        return Promise.resolve({ data: usersMock });
      }
      // Add user
      if (url.includes('users') && method === 'post') {
        return Promise.reject({ response: { data: { error: { message: 'Invalid create parameters' }, errors: { username: 'has already been taken' } } } });
      }
      // Page load get latest template
      if (url.includes('form_templates') && method === 'get') {
        return Promise.resolve({
          data: [
            {
              answers: window.testEnums.answers,
              form_template_id: window.testEnums.form_template_id,
              name: window.testEnums.name,
              version: window.testEnums.version,
            },
          ],
        })
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Click add user button
    fireEvent.click(getByTestId('users-add-user'));

    // Add user dialog appears
    await waitFor(() => {
      expect(getByTestId('add-user'));
    });

    fireEvent.change(getByTestId('add-user-name-first-name'), { target: { value: 'First' } });
    fireEvent.change(getByTestId('add-user-name-last-name'), { target: { value: 'Last' } });
    fireEvent.change(getByTestId('add-user-email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(getByTestId('add-user-username'), { target: { value: 'dan7554' } });
    fireEvent.change(getByTestId('add-user-officer-id'), { target: { value: '987654321' } });
    fireEvent.change(getByTestId('add-user-years'), { target: { value: '12' } });
    fireEvent.change(getByTestId('add-user-dropdown-reviewer'), { target: { value: '10' } });
    fireEvent.change(getByTestId('add-user-password'), { target: { value: 'Ripa_demo1' } });
    fireEvent.change(getByTestId('add-user-confirm-password'), { target: { value: 'Ripa_demo1' } });

    // Try and submit new user
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Submit snack created
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith(
        'create-snack-notification',
        {
          'body': 'Invalid create parameters',
          'level': { 'icon': 'alert-error.svg', 'type': 'error' },
          'title': 'Failed to create user',
        });
    });

    // Add user error message appears
    await waitFor(() => {
      expect(getByTestId('label-field-error'));
    });
  });

  it('adds a invalid user years of experience', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/dashboard']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Lets reset mocks so initial requests will not be recorded
    request.mockClear();

    // Click add user button
    fireEvent.click(getByTestId('users-add-user'));

    // Add user dialog appears
    await waitFor(() => {
      expect(getByTestId('add-user'));
    });

    fireEvent.change(getByTestId('add-user-name-first-name'), { target: { value: 'woofwoof' } });
    fireEvent.change(getByTestId('add-user-name-last-name'), { target: { value: 'man' } });
    fireEvent.change(getByTestId('add-user-email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(getByTestId('add-user-username'), { target: { value: 'wwman' } });
    fireEvent.change(getByTestId('add-user-officer-id'), { target: { value: '123456789' } });
    fireEvent.change(getByTestId('add-user-dropdown-reviewer'), { target: { value: '10' } });
    fireEvent.change(getByTestId('add-user-password'), { target: { value: '@Eqan1097552' } });
    fireEvent.change(getByTestId('add-user-confirm-password'), { target: { value: '@Eqan1097552' } });
    fireEvent.change(getByTestId('add-user-years'), { target: { value: 'e+' } });

    // Submit new user
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Add user error message appears
    await waitFor(() => {
      expect(getByTestId('label-field-error'));
    });
  })

  it('edits an existing user', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/dashboard']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Lets reset mocks so initial requests will not be recorded
    request.mockClear();

    // Click edit user button on user 10
    fireEvent.click(getByTestId('users-row-table-cell-actions-edit-10'));

    // Edit user dialog appears
    await waitFor(() => {
      expect(getByTestId('edit-user'));
    });

    fireEvent.change(getByTestId('edit-user-name-first-name'), { target: { value: 'woofwoof2' } });
    fireEvent.change(getByTestId('edit-user-name-last-name'), { target: { value: 'man2' } });
    fireEvent.change(getByTestId('edit-user-email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(getByTestId('edit-user-username'), { target: { value: 'wwman2' } });
    fireEvent.change(getByTestId('edit-user-officer-id'), { target: { value: 'abc123XYZ' } });
    fireEvent.change(getByTestId('edit-user-years'), { target: { value: '4' } });
    fireEvent.change(getByTestId('edit-user-dropdown-reviewer'), { target: { value: '10' } });

    // Submit update user
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Submit snack created
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith(
        'create-snack-notification',
        {
          'body': 'User updated </br> <small>4321-abc123XYZ</small>',
          'level': { 'icon': 'alert-success.svg', 'type': 'success' },
          'title': 'Success',
        });
    });

    // Reviewer is updated
    await waitFor(() => {
      expect(request).toBeCalledWith({
        url: 'http://localhost/api/v1/reviewerships_by_writer',
        method: 'post',
        data: {
          reviewer_ids: ['10'],
          writer_id: 4321,
        },
        maxRedirects: 0,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      });
    });
  });

  it('does not edit a user with a duplicate email', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url, method }) => {
      // Page load get all users call
      if (url.includes('users') && method === 'get' && !url.includes('limit=300&role=reviewer')) {
        return Promise.resolve({ data: usersMock });
      }
      // Add user
      if (url.includes('users') && method === 'patch') {
        return Promise.reject({ response: { data: { error: { message: 'Invalid update parameters' }, errors: { email: 'has already been taken' } } } });
      }

      if (url.includes('reviewerships') && url.includes('limit=300&reviewer_id')) {
        return Promise.resolve({ data: revieweesMock });
      }

      // Page load get latest template
      if (url.includes('form_templates') && method === 'get') {
        return Promise.resolve({
          data: [
            {
              answers: window.testEnums.answers,
              form_template_id: window.testEnums.form_template_id,
              name: window.testEnums.name,
              version: window.testEnums.version,
            },
          ],
        })
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Click edit user button on user 10
    fireEvent.click(getByTestId('users-row-table-cell-actions-edit-10'));

    // Edit user dialog appears
    await waitFor(() => {
      expect(getByTestId('edit-user'));
    });

    fireEvent.change(getByTestId('edit-user-email'), { target: { value: '<EMAIL>' } });

    // Try and submit user
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Submit snack created
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith(
        'create-snack-notification',
        {
          'body': 'Invalid update parameters',
          'level': { 'icon': 'alert-error.svg', 'type': 'error' },
          'title': 'Failed to update user',
        });
    });

    // Add user error message appears
    await waitFor(() => {
      expect(getByTestId('label-field-error'));
    });
  });

  it('edits an existing user with invalid Years of Experience', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/dashboard']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Lets reset mocks so initial requests will not be recorded
    request.mockClear();

    // Click edit user button on user 10
    fireEvent.click(getByTestId('users-row-table-cell-actions-edit-10'));

    // Edit user dialog appears
    await waitFor(() => {
      expect(getByTestId('edit-user'));
    });

    fireEvent.change(getByTestId('edit-user-name-first-name'), { target: { value: 'woofwoof2' } });
    fireEvent.change(getByTestId('edit-user-name-last-name'), { target: { value: 'man2' } });
    fireEvent.change(getByTestId('edit-user-email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(getByTestId('edit-user-username'), { target: { value: 'wwman2' } });
    fireEvent.change(getByTestId('edit-user-officer-id'), { target: { value: 'abc123XYZ' } });
    fireEvent.change(getByTestId('edit-user-dropdown-reviewer'), { target: { value: '10' } });
    fireEvent.change(getByTestId('edit-user-years'), { target: { value: 'e+' } });

    // Submit update user
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Add user error message appears
    await waitFor(() => {
      expect(getByTestId('label-field-error'));
    });
  })

  it('edits a user password through the edit user dialog', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/dashboard']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Lets reset mocks so initial requests will not be recorded
    request.mockClear();

    // Click edit user button on user 10
    fireEvent.click(getByTestId('users-row-table-cell-actions-edit-10'));

    // Edit user dialog appears
    await waitFor(() => {
      expect(getByTestId('edit-user'));
    });

    // Click change password
    fireEvent.click(getByTestId('edit-user-change-password-toggle'));

    // Change password fields appear
    await waitFor(() => {
      expect(getByTestId('edit-user-password')).toBeInTheDocument();
    });

    // Fill in identical valid passwords
    fireEvent.change(getByTestId('edit-user-password'), { target: { value: 'Pass123!' } });
    fireEvent.change(getByTestId('edit-user-confirm-password'), { target: { value: 'Pass123!' } });

    // Click save
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Submit snack created
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith(
        'create-snack-notification',
        {
          'body': 'User updated </br> <small>4321-TYCJ1DMM7</small>',
          'level': { 'icon': 'alert-success.svg', 'type': 'success' },
          'title': 'Success',
        });
    });

    // Submit password change request
    await waitFor(() => {
      expect(request).toBeCalledWith({
        url: 'http://localhost/api/v1/users/10',
        method: 'patch',
        data: {
          email: "<EMAIL>",
          first_name: "testest",
          last_name: "testestt",
          officer_id: "TYCJ1DMM7",
          organization_id: undefined,
          password: 'Pass123!',
          password_confirmation: 'Pass123!',
          roles: ["3", "2", "1"],
          username: "testestest",
          years_of_experience: 3,
        },
        maxRedirects: 0,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      });
    });
  });

  it('edits the user reviewer role from true to false through the edit user dialog and the removing reviewer dialog', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/dashboard']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    // Wait for api to return user rows
    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });

    // Lets reset mocks so initial requests will not be recorded
    request.mockClear();

    // Click edit user button on user 10
    fireEvent.click(getByTestId('users-row-table-cell-actions-edit-10'));

    // Edit user dialog appears
    await waitFor(() => {
      expect(getByTestId('edit-user'));
    });

    //Click the reviewer checkbox
    fireEvent.click(getByTestId('edit-user-checkbox-reviewer'));

    // Click save
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Removing Reviewer user dialog appears
    await waitFor(() => {
      expect(getByTestId('removing-reviewer-dialog'));
    });

    // Click save from warning removing reviewer dialog
    fireEvent.click(getByTestId('removing-reviewer-confirm-dialog-yes-button'));

    // Submit snack created
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith(
        'create-snack-notification',
        {
          'body': 'User updated </br> <small>4321-TYCJ1DMM7</small>',
          'level': { 'icon': 'alert-success.svg', 'type': 'success' },
          'title': 'Success',
        });
    });
  });

  it('renders for mobile', () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url }) => {
      if (url.includes('users')) {
        return Promise.resolve({ data: usersMock });
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    });

    // xs page width
    store.dispatch(Config.updatePageWidth(375));

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    expect(getByTestId('users-mobile')).toBeInTheDocument();
  });

  it('uses lazy loading successfully on mobile', async () => {
    const store = createStore();

    // Http client will return mocks
    request.mockImplementation(({ url, method }) => {
      const query = qs.parse(url.split('?')[1]);
      if (url.includes('users') && method === 'get') {
        return Promise.resolve({
          data: {
            users: usersMock.users,
            pagination: {
              pages: 2,
              count: 20,
              page: query.page,
              items: query.limit,
            },
          },
        });
      }
      // Page load get latest template
      if (url.includes('form_templates') && method === 'get') {
        return Promise.resolve({
          data: [
            {
              answers: window.testEnums.answers,
              form_template_id: window.testEnums.form_template_id,
              name: window.testEnums.name,
              version: window.testEnums.version,
            },
          ],
        })
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    });

    // xs page width
    store.dispatch(Config.updatePageWidth(375));

    const { getByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    expect(getByTestId('users-mobile')).toBeInTheDocument();

    const cardsWrapper = getByTestId('users-mobile-cards');

    await waitFor(() => {
      expect(cardsWrapper.childElementCount).toBe(16);
    });

    fireEvent.scroll(cardsWrapper, { target: { scrollY: cardsWrapper.clientHeight } });

    await waitFor(() => {
      expect(cardsWrapper.childElementCount).toBe(32);
    });
  });

  it('disabled account with no disable option on mobile', async () => {
    const store = createStore();

    request.mockImplementation(({ url }) => {
      if (url.includes('users')) {
        return Promise.resolve({
          data: {
            pagination: { count: 1, page: 1, items: 1, pages: 1 },
            users: [{
              id: 1,
              email: '<EMAIL>',
              created_at: '2021-04-05T21:51:02.421Z',
              updated_at: '2021-04-07T18:09:22.569Z',
              organization_id: 2,
              username: 'officer01',
              first_name: 'of',
              last_name: 'dv',
              roles: ['officer'],
              officer_id: 'TYCJ1DMM7',
              deleted_at: null,
              years_of_experience: 3,
              reviewers: [],
              'access_locked?': true
            }],
          }
        });
      }
      if (url.includes('organizations')) {
        return Promise.resolve({ data: orgsMock });
      }
      return Promise.resolve({ data: {} })
    })

    store.dispatch(Config.updatePageWidth(375));

    const { getByTestId, queryByTestId } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );
    await sleep(200);

    await waitFor(() => {
      expect(getByTestId('users-mobile-cards')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('users-mobile-card-actions-0'));

    await waitFor(() => {
      expect(queryByTestId('users-mobile-menu-item-delete')).not.toBeInTheDocument();
    });
  });
  it('should select all reviewer when select all option', async () => {
    const store = createStore();
    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId, getByText } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });
    const reviewer = getByTestId('users-dropdown-assign-reviewer');
    fireEvent.change(reviewer, { target: { value: ['all'] } });
    expect(getByText(/all reviewers/i)).toBeInTheDocument();
  });
  it('should deselect all reviewer when uncheck all option', async () => {
    const store = createStore();
    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId, getByText, queryByText } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });
    const reviewer = getByTestId('users-dropdown-assign-reviewer');
    fireEvent.change(reviewer, { target: { value: ['all'] } });
    expect(getByText(/all reviewers/i)).toBeInTheDocument();
    fireEvent.change(reviewer, { target: { value: [] } });
    expect(queryByText(/all reviewers/i)).not.toBeInTheDocument();
  });
  it('should render Select All option when there select all reviewer manually', async () => {
    const store = createStore();
    // Http client will return mocks
    request.mockImplementation(usersApiMocks);

    const { getByTestId, getByText } = render(
      <Provider store={store}>
        <DecoupledDispatchProvider store={store}>
          <MemoryRouter initialEntries={['/admin/users']} initialIndex={0}>
            <Users />
          </MemoryRouter>
        </DecoupledDispatchProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId('users-row-cell-0-11-0')).toBeInTheDocument();
    });
    const reviewer = getByTestId('users-dropdown-assign-reviewer');
    fireEvent.change(reviewer, { target: { value: ['5', '10', '11'] } });
    expect(getByText(/all reviewers/i)).toBeInTheDocument();
  });
});
