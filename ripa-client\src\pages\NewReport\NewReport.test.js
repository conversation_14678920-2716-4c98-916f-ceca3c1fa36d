import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { Provider, connect } from 'react-redux';
import createStore from '@engine/store';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';
import { NewReport as NewReportDuck, Form, User } from '@ducks';
import { history, clearHistory } from '@utility/assignRoute';
import StepsFunction from '@pages/NewReport/Steps';
import { request } from 'axios';
import { setTemplateLoading } from '@ducks/template';
import { dispatchCustomEvent } from '@utility/customEvent';
import sleep from '@utility/sleep';
import NewReport, { NewReport as NewReportNoStore, mapDispatchToProps, mapStateToProps } from './NewReport';

export const NewReportNoRouter = connect(mapStateToProps, mapDispatchToProps)(NewReportNoStore);

const Steps = StepsFunction({ enumMap: { PerceivedToBeUnhoused: false } }, { pre2024: false, post2024: false });

// Test may run a while
jest.setTimeout(30000);

jest.mock('axios', () => ({ request: jest.fn() }));
jest.mock('@utility/customEvent', () => ({ dispatchCustomEvent: jest.fn() }));

Object.assign(navigator, {
  clipboard: {
    writeText: () => {},
  },
});

const calendarToPeople = async ({ getByTestId }) => {
  expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-time-form')).toBeInTheDocument();
  });
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-response-to-call-form')).toBeInTheDocument();
  });
  fireEvent.click(getByTestId('ripa-response-to-call-yes'));

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-duration-form')).toBeInTheDocument();
  });
  fireEvent.click(getByTestId('ripa-duration-form-10'));

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-location-form')).toBeInTheDocument();
  });

  fireEvent.change(getByTestId('ripa-location-form-address-input'), {
    target: { value: 'Aberdeen' },
  });

  fireEvent.change(getByTestId('ripa-location-form-city-autocomplete-input'), {
    target: { value: 'BROOKDALE' },
  });

  fireEvent.keyDown(getByTestId('ripa-location-form-city-autocomplete-input'), { key: 'ArrowDown' });
  fireEvent.keyDown(getByTestId('ripa-location-form-city-autocomplete-input'), { key: 'Enter' });

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-people-form')).toBeInTheDocument();
  });
};

const fromStartToActionFormWith2People = async ({ getByTestId, store }) => {
  store.dispatch(Form.setFormField({ path: ['id'] })('10001'));
  await calendarToPeople({ getByTestId });
  fireEvent.click(getByTestId('ripa-people-form-people-counter-plus'));

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-label-form')).toBeInTheDocument();
  });

  fireEvent.change(getByTestId('ripa-label-form-label-input-0'), {
    target: { value: 'label 1' },
  });

  fireEvent.change(getByTestId('ripa-label-form-label-input-1'), {
    target: { value: 'label 2' },
  });
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-gender-form')).toBeInTheDocument();
  });

  fireEvent.click(getByTestId('ripa-gender-form-box-0-0'));
  fireEvent.click(getByTestId('ripa-gender-form-box-1-0'));

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-age-form')).toBeInTheDocument();
  });

  store.dispatch(Form.setFormField({ path: ['person', 0, 'age'] })(18));
  store.dispatch(Form.setFormField({ path: ['person', 1, 'age'] })(18));

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-race-form')).toBeInTheDocument();
  });

  fireEvent.click(getByTestId('ripa-race-form-0-0'));
  fireEvent.click(getByTestId('ripa-race-form-1-0'));

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-disability-form')).toBeInTheDocument();
  });

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
  });

  fireEvent.click(getByTestId('ripa-primary-reason-form-use-same-switch'));
  fireEvent.click(getByTestId('ripa-primary-reason-form-box-0'));
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-traffic-violation-form')).toBeInTheDocument();
  });

  fireEvent.click(getByTestId('ripa-traffic-violation-form-box-0'));
  fireEvent.change(
    getByTestId('ripa-traffic-violation-form-autocomplete-input'),
    { target: { value: '32 - ACCESSORY' } },
  );

  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // wait ripa-description-form
  await waitFor(() => {
    expect(getByTestId('ripa-description-form')).toBeInTheDocument();
  });

  fireEvent.change(getByTestId('ripa-description-form-input'), {
    target: { value: 'description' },
  });

  fireEvent.click(getByTestId('ripa-form-container-continue'));
  await waitFor(() => {
    expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
  });
};

const fullStep4ForOnePerson = async ({ getByTestId }) => {
  // 4.1
  await waitFor(() => {
    expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
  });

  // Click 'Person removed from vehicle by order' action
  fireEvent.click(getByTestId('ripa-action-taken-form-box-0-0'));

  // Click 'Person removed from vehicle by physical contact' action
  fireEvent.click(getByTestId('ripa-action-taken-form-box-0-1'));

  // Click 'Field sobriety test conducted' action
  fireEvent.click(getByTestId('ripa-action-taken-form-box-0-2'));

  // Click 'Vehicle impounded' action
  fireEvent.click(getByTestId('ripa-action-taken-form-box-0-3'));

  // Click on Search tab
  fireEvent.click(getByTestId('ripa-action-taken-form-action-tab-4'));

  // Click 'Property was seized' action
  fireEvent.click(getByTestId('ripa-action-taken-form-box-4-4'));

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // wait ripa-seizure-basis-form 4.4
  await waitFor(() => {
    expect(getByTestId('ripa-seizure-basis-form')).toBeInTheDocument();
  });

  // Select to continue step
  fireEvent.click(getByTestId('ripa-seizure-basis-form-box-0'));

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // wait ripa-seizure-type-form 4.5
  await waitFor(() => {
    expect(getByTestId('ripa-seizure-type-form')).toBeInTheDocument();
  });

  // Select to continue step
  fireEvent.click(getByTestId('ripa-seizure-type-form-box-0'));

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
  });

  fireEvent.click(getByTestId('ripa-contraband-form-box-0'));

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  await waitFor(() => {
    expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
  });

  // Select to continue step
  fireEvent.click(getByTestId('ripa-result-of-stop-form-box-0-0'));
  fireEvent.change(
    getByTestId('ripa-result-of-stop-form-autocomplete-input-0-0'),
    { target: { value: '32 - ACCESSORY' } },
  );

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));
};

const fullStep5 = async ({ getByTestId, store }) => {
  // setValue to pass validate to all subStep
  store.dispatch(Form.setFormField({ path: ['whenRaceGenderKnown'] })({}));
  store.dispatch(Form.setFormField({ path: ['beat'] })('beat'));
  store.dispatch(
    Form.setFormField({ path: ['whyStopInitiated'] })('whyStopInitiated'),
  );
  store.dispatch(
    Form.setFormField({ path: ['stopMatchRaceGender'] })('stopMatchRaceGender'),
  );
  store.dispatch(Form.setFormField({ path: ['crimeAware'] })('crimeAware'));
  store.dispatch(
    Form.setFormField({ path: ['paroleProbation'] })('paroleProbation'),
  );
  store.dispatch(
    Form.setFormField({ path: ['knownGangMember'] })('knownGangMember'),
  );

  // 5.1
  await waitFor(() => {
    expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
  });

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // 5.2
  await waitFor(() => {
    expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
  });

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // 5.3
  await waitFor(() => {
    expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
  });

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // 5.4
  await waitFor(() => {
    expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
  });

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // 5.5
  await waitFor(() => {
    expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
  });

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // 5.6
  await waitFor(() => {
    expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
  });

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // 5.7
  await waitFor(() => {
    expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
  });

  // Click continue
  fireEvent.click(getByTestId('ripa-form-container-continue'));

  // 6.1
  await waitFor(() => {
    expect(getByTestId('ripa-review-form')).toBeInTheDocument();
  });
};

describe('Response to network errors', () => {
  jest.spyOn(navigator.clipboard, 'writeText');
  beforeEach(() => {
    const featureFlags = JSON.parse(process.env.REACT_APP_FEATURE_FLAGS);
    featureFlags.previewPost2024 = "2024-01-01T08:00:00Z";
    process.env.REACT_APP_FEATURE_FLAGS = JSON.stringify(featureFlags);

    Element.prototype.scrollTo = jest.fn();
    jest.clearAllMocks();

  });

  // TODO: fix this 
  it.skip('does middleware update appropriately, throws a snack if an update fails due to a network error', async () => {
    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' }, user: { id: '1', organization_id: 1, email: '<EMAIL>' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Patch will resolve
    request.mockImplementation(() => Promise.reject({ message: 'Network Error' }));

    // Set a form id
    store.dispatch(Form.setFormField({ path: ['id'] })('13'));

    // Set to first step
    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Start on 1.1
    expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();

    await fromStartToActionFormWith2People({ getByTestId, store });

    // Click 'Person removed from vehicle by order' action
    fireEvent.click(getByTestId('ripa-action-taken-form-box-0-0'));

    // Click 'Person removed from vehicle by physical contact' action
    fireEvent.click(getByTestId('ripa-action-taken-form-box-0-1'));

    // Click 'Field sobriety test conducted' action
    fireEvent.click(getByTestId('ripa-action-taken-form-box-0-2'));

    // Click 'Vehicle impounded' action
    fireEvent.click(getByTestId('ripa-action-taken-form-box-0-3'));

    // Should only call patch due to middleware throttle
    await waitFor(
      () => {
        expect(request).toBeCalled();
      },
      { timeout: 3500 },
    );

    // Snack is thrown
    await waitFor(
      () => {
        expect(dispatchCustomEvent).toBeCalledWith('create-snack-notification',
          {
            body: 'Are you connected to the internet?',
            level: { icon: 'alert-error.svg', type: 'error' },
            title: 'Network Error',
          }),
          { timeout: 3500 };
      });
  });
});

describe('NewReport page', () => {
  jest.spyOn(navigator.clipboard, 'writeText');
  beforeEach(() => {
    const featureFlags = JSON.parse(process.env.REACT_APP_FEATURE_FLAGS);
    featureFlags.previewPost2024 = "2024-01-01T08:00:00Z";
    process.env.REACT_APP_FEATURE_FLAGS = JSON.stringify(featureFlags);

    Element.prototype.scrollTo = jest.fn();
  });

  it('renders', () => {
    const store = createStore();

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    expect(getByTestId('new-report')).toBeInTheDocument();
  });

  it('goes to 1.2 and goes back to 1.1', async () => {
    request.mockImplementation(() => Promise.resolve({}));

    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Start on 1.1
    expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 1.2 appears
    await waitFor(() => {
      expect(getByTestId('ripa-time-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 1.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();
    });
  });

  it('goes to 1.2 and update isValidStopTime on duplicate report api 409 response', async () => {
    request.mockImplementation(({ url, method }) => {
      if (url.includes('valid_time') && method === 'get') {
        return Promise.reject({
          response: {
            status: 409,
          },
        });
      }
      return Promise.resolve({});
    });

    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' } }));
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }));
    store.dispatch(Form.setFormField({ path: ['id'] })('10001'));

    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(
      NewReportDuck.registerSteps({
        nSteps: Steps.map(({ substeps, title, loop }) => ({ title, loop, nSubsteps: substeps.length, preconditions: substeps.map((s) => s.precondition) })),
        steps: Steps,
      })
    );
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Start on 1.1
    expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 1.2 appears
    await waitFor(() => {
      expect(getByTestId('ripa-time-form')).toBeInTheDocument();
    });

    // Update the time
    fireEvent.change(getByTestId('ripa-time-form-input'), { target: { value: '12:30' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // Check store for isValidStopTime update
    expect(store.getState().Form.isValidStopTime).toBe(false);
  });

  it('does middleware updates appropriately, and navigates preconditions, and performs a subloop', async () => {
    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Patch will resolve
    request.mockImplementation(() => Promise.resolve());

    // Set a form id
    store.dispatch(Form.setFormField({ path: ['id'] })('13'));

    // Set to first step
    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    await fromStartToActionFormWith2People({ getByTestId, store });

    // people 1
    await fullStep4ForOnePerson({ getByTestId });

    await waitFor(
      () => {
        expect(request).toBeCalled();
      },
      { timeout: 3500 },
    );

    // people 2
    await fullStep4ForOnePerson({ getByTestId });

    await waitFor(
      () => {
        expect(request).toHaveBeenCalled();
      },
      { timeout: 3500 },
    );

    await fullStep5({ getByTestId, store });

    await waitFor(() => {
      expect(getByTestId('ripa-review-form')).toBeInTheDocument();
    });

    await waitFor(
      () => {
        expect(request).toHaveBeenCalled();
      },
      { timeout: 3500 },
    );
  });

  it('goes to 1.4 and goes back to 1.1, hits back again and it doesnt break', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))
    store.dispatch(Form.setFormField({ path: ['id'] })('10001'));

    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Start on 1.1
    expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 1.2 appears
    await waitFor(() => {
      expect(getByTestId('ripa-time-form')).toBeInTheDocument();
    });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 1.3 appears
    await waitFor(() => {
      expect(getByTestId('ripa-response-to-call-form')).toBeInTheDocument();
    });

    // Click  Response to Call 'Yes'
    fireEvent.click(getByTestId('ripa-response-to-call-yes'));

    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 1.4 appears
    await waitFor(() => {
      expect(getByTestId('ripa-duration-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 1.2 appears
    await waitFor(() => {
      expect(getByTestId('ripa-time-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 1.1 is rendered
    await waitFor(() => {
      expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 1.1 is still rendered
    await waitFor(() => {
      expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();
    });
  });

  it('goes from start to action form with 2 people and navigate backward', async () => {
    request.mockImplementation(() => Promise.resolve({}));

    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    await fromStartToActionFormWith2People({ getByTestId, store });
    
    // 4.1
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 3.5
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 3.2
    await waitFor(() => {
      expect(getByTestId('ripa-traffic-violation-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 3.1
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 2.6
    await waitFor(() => {
      expect(getByTestId('ripa-disability-form')).toBeInTheDocument();
    });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.1
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });
  });

  it('goes to 4.1 from 3.5, performs one subloop, then advances to 5.1, and goes back to 3.5', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to last substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 4, subloop: -1, component: Steps[2].substeps[4].component }));

    // Set normal steps
    store.dispatch(
      NewReportDuck.registerSteps({
        nSteps: Steps.map(({ substeps, title, loop }) => ({
          title,
          loop,
          nSubsteps: substeps.length,
          preconditions: substeps.map((s) => s.precondition),
        })),
        steps: Steps,
      }),
    );
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people to 2
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(2));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Start on 3.5
    expect(getByTestId('ripa-description-form')).toBeInTheDocument();

    const ripaDescriptionFormInput = getByTestId('ripa-description-form-input');

    expect(ripaDescriptionFormInput).toBeInTheDocument();

    fireEvent.change(ripaDescriptionFormInput, {
      target: { value: 'Description' },
    });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('ripa-action-taken-form-box-0-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // wait ripa-contraband-form
    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
    });

    // Toggle to no contraband or evidence discovered
    fireEvent.click(getByTestId('ripa-contraband-form-any-found-switch'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.7 appears
    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });

    // Select to continue step
    fireEvent.click(getByTestId('ripa-result-of-stop-form-box-0-0'));
    fireEvent.change(
      getByTestId('ripa-result-of-stop-form-autocomplete-input-0-0'),
      { target: { value: '32 - ACCESSORY' } },
    );

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('ripa-action-taken-form-box-0-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
    });

    // Click nothing found
    fireEvent.click(getByTestId('ripa-contraband-form-any-found-switch'));
    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.7 appears
    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });

    // Select to continue step
    fireEvent.click(getByTestId('ripa-result-of-stop-form-box-0-0'));
    fireEvent.change(
      getByTestId('ripa-result-of-stop-form-autocomplete-input-0-0'),
      { target: { value: '32 - ACCESSORY' } },
    );

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 5.1 appears DynamicSelectForm
    await waitFor(() => {
      expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // Back on 4.7
    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // Back on 4.1
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // Back on 4.6
    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // wait ripa-seizure-type-form 4.6
    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // Back on 4.1
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });
  });

  it('hits the last substep and goes to next step and back', async () => {
    request.mockImplementation(() => Promise.resolve({}));

    const store = createStore();

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Register less steps to make this test easier
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: [
        { nSubsteps: 1 },
        { nSubsteps: 1 },
      ],
      steps: Steps,
    }));

    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));
    // Start on 1a.1
    expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 2.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-people-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 1.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();
    });
  });

  it('gets the form when id is in path and renders the first component', async () => {
    const store = createStore();

    request.mockImplementation(({ url, method }) => {
      // Get user forms will return mock
      if (url.includes('form_data') && method === 'get') {
        return Promise.resolve({ data: { contents: {} } });
      }
      // use data in setupTests
      if (url.includes('data') && method === 'get') {
        return Promise.resolve({ data: null });
      }
      return Promise.resolve()
    })

    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReportNoRouter match={{ params: { id: '34' } }} />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Loading renders during api call
    await waitFor(() => {
      expect(getByTestId('loading')).toBeInTheDocument();
    });

    // 1.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();
    });
  });

  it('redirects to dashboard if there is no id in the path', async () => {
    const store = createStore();

    request.mockImplementation(({ url, method }) => {
      // Get user forms will return mock
      if (url.includes('form_data') && method === 'get') {
        return Promise.resolve({ data: { contents: {} } });
      }
      // use data in setupTests
      if (url.includes('data') && method === 'get') {
        return Promise.resolve({ data: null });
      }
      return Promise.resolve()
    })

    render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReportNoRouter match={{ params: { id: '' } }} />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Redirect to dashboard
    await waitFor(() => {
      expect(history[history.length - 1]).toBe('/dashboard');
    });
  });

  it('throws a snack and redirects to dashboard if getForm fails on a Network Error', async () => {
    const store = createStore();
    clearHistory();

    // Get user form will reject
    request.mockImplementation(() => Promise.reject({ message: 'Network Error' }));

    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReportNoRouter match={{ params: { id: '34' } }} />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Snack is thrown
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalled();
    });

    // Goes to dashboard on error
    await waitFor(() => {
      expect(history.last()).toContain('/dashboard');
    }, { timeout: 3000 });
    // 4.1 appears
  });

  it('throws a snack and redirects to dashboard if getForm fails on a Unknown Error', async () => {
    const store = createStore();

    // Get user form will reject
    request.mockImplementation(() => Promise.reject({ message: 'Unknown 222' }));

    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReportNoRouter match={{ params: { id: '34' } }} />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Snack is thrown
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalled();
    }, { timeout: 10000 });

    // Redirect to dashboard
    await waitFor(() => {
      expect(history[history.length - 1]).toBe('/dashboard');
    }, { timeout: 10000 });
  });

  it('makes a snack on start over button', async () => {
    const store = createStore();

    // Get user form will reject
    request.mockImplementation(() => Promise.reject({ messages: 'Unknown 222' }));

    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReportNoRouter match={{ params: { id: '34' } }} />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Click start over button
    fireEvent.click(getByTestId('new-report-drawer-start-over-btn'));

    // Start over confirm dialog opens
    await waitFor(() => {
      fireEvent.click(getByTestId('new-report-start-over-dialog'));
    });

    // Click yes
    fireEvent.click(getByTestId('confirm-dialog-yes-button'));

    // Snack made
    expect(dispatchCustomEvent).toBeCalled();
  });

  it('Select consensual encounter on primary reason for stop', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to first substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 0, subloop: 0, component: Steps[2].substeps[0].component }));

    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(1));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    await sleep(100)

    // 3.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // Select consensual encounter
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-5'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });

    const ripaDescriptionFormInput = getByTestId('ripa-description-form-input');

    expect(ripaDescriptionFormInput).toBeInTheDocument();

    fireEvent.change(ripaDescriptionFormInput, { target: { value: 'Description' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    // 4.1 tab search appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form-action-tab-4')).toBeInTheDocument();
    });

    // Click on inessential option
    fireEvent.click(getByTestId('ripa-action-taken-form-box-4-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // Snack is thrown
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith('create-snack-notification',
        {
          body: 'When “Consensual Encounter resulting in a search” is selected, you must select a search action.',
          level: { icon: 'alert-error.svg', type: 'error' },
          title: 'Action taken required',
        }),
        { timeout: 2000 };
    });

    // 4.1 tab search appears
    expect(getByTestId('ripa-action-taken-form-action-tab-4')).toBeInTheDocument();

    // Click on mandatory option
    fireEvent.click(getByTestId('ripa-action-taken-form-box-4-1'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.2 appears
    await waitFor(() => {
      expect(getByTestId('ripa-search-basis-form')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('ripa-search-basis-form-box-1'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-search-description-form')).toBeInTheDocument();
    });

    const ripaSearchDescriptionFormInput1 = getByTestId('ripa-search-description-form-input');

    expect(ripaSearchDescriptionFormInput1).toBeInTheDocument();

    fireEvent.change(ripaSearchDescriptionFormInput1, { target: { value: 'Description' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // wait ripa-seizure-type-form 4.6
    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
    });

    // No contraband found
    fireEvent.click(getByTestId('ripa-contraband-form-any-found-switch'));
    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.7 appears
    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });

    // Select to continue step
    fireEvent.click(getByTestId('ripa-result-of-stop-form-box-0-0'));
    fireEvent.change(
      getByTestId('ripa-result-of-stop-form-autocomplete-input-0-0'),
      { target: { value: '32 - ACCESSORY' } },
    );

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 5.1
    await waitFor(() => {
      expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
    });
  });

  it('selects multiple option when chose reasonable suspicion', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to first substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 0, subloop: 0, component: Steps[2].substeps[0].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))
    store.dispatch(Form.setFormField({ path: ['stopDateTime'] })('2023-01-05T00:03:00.000Z'))
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // 3.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // Select reasonable suspicion
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-1'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.3 appears
    await waitFor(() => {
      expect(getByTestId('ripa-suspicion-form')).toBeInTheDocument();
    });

    // click multiple options
    fireEvent.click(getByTestId('ripa-suspicion-form-box-0'));

    expect(getByTestId('ripa-suspicion-form-box-0')).toHaveClass('active');

    fireEvent.click(getByTestId('ripa-suspicion-form-box-1'));

    expect(getByTestId('ripa-suspicion-form-box-1')).toHaveClass('active');

    fireEvent.click(getByTestId('ripa-suspicion-form-box-2'));

    expect(getByTestId('ripa-suspicion-form-box-2')).toHaveClass('active');

    fireEvent.click(getByTestId('ripa-suspicion-form-box-3'));

    expect(getByTestId('ripa-suspicion-form-box-3')).toHaveClass('active');

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });
  });

  it('loops through Primary Reason twice when two people', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to first substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 0, subloop: 0, component: Steps[2].substeps[0].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(2));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // 3.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // Select 'Consensual Encounter resulting in a search'
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-5'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });

    expect(getByTestId('ripa-description-form-input')).toBeInTheDocument();

    fireEvent.change(getByTestId('ripa-description-form-input'), { target: { value: 'Description' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.1 appears again
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // 'Consensual Encounter resulting in a search' should not be select
    expect(getByTestId('ripa-primary-reason-form-box-5')).not.toHaveClass('active');

    // Select 'Consensual Encounter resulting in a search'
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-5'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });

    expect(getByTestId('ripa-description-form-input')).toBeInTheDocument();

    fireEvent.change(getByTestId('ripa-description-form-input'), { target: { value: 'Description' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });
  });

  it('only goes through Primary Reason once when two people and Use Same For All', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to first substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 0, subloop: 0, component: Steps[2].substeps[0].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(2));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // 3.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // Toggle Use Same For All
    fireEvent.click(getByTestId('ripa-primary-reason-form-use-same-switch'));

    // Select 'Consensual Encounter resulting in a search'
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-5'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });

    expect(getByTestId('ripa-description-form-input')).toBeInTheDocument();

    fireEvent.change(getByTestId('ripa-description-form-input'), { target: { value: 'Description' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });
  });

  it('navigate back through Primary Reason with two people and Use Same For All is toggled on', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to substep 6 of step 2
    store.dispatch(NewReportDuck.setStep({ step: 1, substep: 5, subloop: 0, component: Steps[1].substeps[5].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(2));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // 2.6
    await waitFor(() => {
      expect(getByTestId('ripa-disability-form')).toBeInTheDocument();
    });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.1
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // Toggle Use Same For All
    fireEvent.click(getByTestId('ripa-primary-reason-form-use-same-switch'));

    // Toggle Use Same For All is on
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form-use-same-switch')).toHaveClass('is-on');
    });

    // Select 'Traffic Violation'
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.2
    await waitFor(() => {
      expect(getByTestId('ripa-traffic-violation-form')).toBeInTheDocument();
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));
    
    // 3.1
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });
  });

  it('disables "Use Same For All" toggle when 1 person', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to first substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 0, subloop: 0, component: Steps[2].substeps[0].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(1));

    const { getByTestId, queryByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // 3.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // 'Use Same For All' does not render
    expect(queryByTestId('ripa-primary-reason-form-use-same-switch')).toHaveClass('disabled');
  });

  it('disables "Use Same For All" toggle on subsequent loops', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to first substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 0, subloop: 0, component: Steps[2].substeps[0].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(2));

    const { getByTestId, queryByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // 3.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // 'Use Same For All' renders
    expect(queryByTestId('ripa-primary-reason-form-use-same-switch')).toBeInTheDocument();

    // Select 'Consensual Encounter resulting in a search'
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-5'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });

    expect(getByTestId('ripa-description-form-input')).toBeInTheDocument();

    fireEvent.change(getByTestId('ripa-description-form-input'), { target: { value: 'Description' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.1 appears again
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // 'Use Same For All' does not render
    expect(queryByTestId('ripa-primary-reason-form-use-same-switch')).toHaveClass('disabled');
  });

  it('selects consent given enable/disable with yes/no option', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to first substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 0, subloop: 0, component: Steps[2].substeps[0].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(1));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // 3.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    // Select consensual encounter
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-5'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });

    const ripaDescriptionFormInput = getByTestId('ripa-description-form-input');

    expect(ripaDescriptionFormInput).toBeInTheDocument();

    fireEvent.change(ripaDescriptionFormInput, { target: { value: 'Description' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('ripa-action-taken-form-box-4-1'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-search-basis-form')).toBeInTheDocument();
    });

    // disabled
    await waitFor(() => {
      expect(getByTestId('ripa-search-basis-form-box-0')).toHaveClass('disable');
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('ripa-action-taken-form-action-tab-4'))

    // action-tab-4 is showing
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form-box-4-0')).toBeInTheDocument()
    })

    fireEvent.click(getByTestId('ripa-action-taken-form-box-4-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-search-basis-form')).toBeInTheDocument();
    });

    // disabled
    await waitFor(() => {
      expect(getByTestId('ripa-search-basis-form-box-0')).toHaveClass('disable');
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('ripa-action-taken-form-action-tab-4'))

    // action-tab-4 is showing
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form-box-4-0')).toBeInTheDocument()
    })

    fireEvent.click(getByTestId('ripa-action-taken-form-consent-switch-4-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-search-basis-form')).toBeInTheDocument();
    });

    // enabled
    await waitFor(() => {
      expect(getByTestId('ripa-search-basis-form-box-0')).not.toHaveClass('disable');
    });

    fireEvent.click(getByTestId('ripa-search-basis-form-box-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-search-description-form')).toBeInTheDocument();
    });
  });

  it('shows contraband/evidence form always but does not require response', async () => {
    request.mockImplementation(() => Promise.resolve({}));

    const store = createStore();

    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    await fromStartToActionFormWith2People({ getByTestId, store });

    // 4.1
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    // Click 'Person removed from vehicle by order' action
    fireEvent.click(getByTestId('ripa-action-taken-form-box-0-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
      const anySwitch = getByTestId('ripa-contraband-form-any-found-switch');
      expect(anySwitch).not.toHaveClass('disabled');
      expect(anySwitch).toHaveClass('is-on');
    });

    fireEvent.click(getByTestId('ripa-contraband-form-any-found-switch'));
    expect(getByTestId('ripa-contraband-form-any-found-switch')).not.toHaveClass('is-on');

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });
  });

  it('shows contraband/evidence form when seizure basis includes Contraband and requires response', async () => {
    request.mockImplementation(() => Promise.resolve({}));

    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    await fromStartToActionFormWith2People({ getByTestId, store });

    // 4.1
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    // Click on Search tab
    fireEvent.click(getByTestId('ripa-action-taken-form-action-tab-4'));
    // Click 'Property was seized' action
    fireEvent.click(getByTestId('ripa-action-taken-form-box-4-4'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // wait ripa-seizure-basis-form 4.4
    await waitFor(() => {
      expect(getByTestId('ripa-seizure-basis-form')).toBeInTheDocument();
    });

    // Select contraband
    fireEvent.click(getByTestId('ripa-seizure-basis-form-box-1'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // wait ripa-seizure-type-form 4.5
    await waitFor(() => {
      expect(getByTestId('ripa-seizure-type-form')).toBeInTheDocument();
    });

    // firearm seized
    fireEvent.click(getByTestId('ripa-seizure-type-form-box-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
      expect(getByTestId('ripa-contraband-form-any-found-switch')).toHaveClass('is-on disabled')
    });

    // Select alcohol
    fireEvent.click(getByTestId('ripa-contraband-form-box-5'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });
  });

  it('shows contraband/evidence form when seizure basis includes Evidence and requires response', async () => {
    request.mockImplementation(() => Promise.resolve({}));

    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));
    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    await fromStartToActionFormWith2People({ getByTestId, store });

    // 4.1
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    // Click on Search tab
    fireEvent.click(getByTestId('ripa-action-taken-form-action-tab-4'));
    // Click 'Property was seized' action
    fireEvent.click(getByTestId('ripa-action-taken-form-box-4-4'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // wait ripa-seizure-basis-form 4.4
    await waitFor(() => {
      expect(getByTestId('ripa-seizure-basis-form')).toBeInTheDocument();
    });

    // Select evidence
    fireEvent.click(getByTestId('ripa-seizure-basis-form-box-2'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // wait ripa-seizure-type-form 4.5
    await waitFor(() => {
      expect(getByTestId('ripa-seizure-type-form')).toBeInTheDocument();
    });

    // firearm seized
    fireEvent.click(getByTestId('ripa-seizure-type-form-box-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
      expect(getByTestId('ripa-contraband-form-any-found-switch')).toHaveClass('is-on disabled');
    });

    // Select weapon
    fireEvent.click(getByTestId('ripa-contraband-form-box-4'));
    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });
  });

  it('can edit contraband/evidence form if seizure basis is changed after selecting none', async () => {
    request.mockImplementation(() => Promise.resolve({}));

    const store = createStore();
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    await fromStartToActionFormWith2People({ getByTestId, store });

    // 4.1
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    // Click on Search tab
    fireEvent.click(getByTestId('ripa-action-taken-form-action-tab-4'));
    // Click 'Property was seized' action
    fireEvent.click(getByTestId('ripa-action-taken-form-box-4-4'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // wait ripa-seizure-basis-form 4.4
    await waitFor(() => {
      expect(getByTestId('ripa-seizure-basis-form')).toBeInTheDocument();
    });

    // Select safekeeping
    fireEvent.click(getByTestId('ripa-seizure-basis-form-box-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // wait ripa-seizure-type-form 4.5
    await waitFor(() => {
      expect(getByTestId('ripa-seizure-type-form')).toBeInTheDocument();
    });

    // firearm seized
    fireEvent.click(getByTestId('ripa-seizure-type-form-box-0'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
      const cls = getByTestId('ripa-contraband-form-any-found-switch');
      expect(cls).toHaveClass('is-on');
      expect(cls).not.toHaveClass('disabled');
    });

    // Choose no results
    fireEvent.click(getByTestId('ripa-contraband-form-any-found-switch'));
    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form-any-found-switch')).not.toHaveClass('is-on');
    });

    // Click back
    fireEvent.click(getByTestId('ripa-form-container-back'));

    await waitFor(() => {
      expect(getByTestId('ripa-seizure-type-form')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('ripa-form-container-back'));

    // wait ripa-seizure-basis-form 4.4
    await waitFor(() => {
      expect(getByTestId('ripa-seizure-basis-form')).toBeInTheDocument();
    });

    // Select Evidence
    fireEvent.click(getByTestId('ripa-seizure-basis-form-box-2'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-seizure-type-form')).toBeInTheDocument();
    });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    await waitFor(() => {
      expect(getByTestId('ripa-contraband-form')).toBeInTheDocument();
      expect(getByTestId('ripa-contraband-form-any-found-switch')).toHaveClass('is-on disabled');
    });
  });

  it('disable validation when toggle any result is off', async () => {
    request.mockImplementation(() => Promise.resolve({}));
    const store = createStore();

    // Set to near last substep of step 4
    store.dispatch(NewReportDuck.setStep({ step: 3, substep: 6, subloop: 0, component: Steps[3].substeps[6].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(1));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form')).toBeInTheDocument();
    });

    // Tab 0 active
    expect(getByTestId('ripa-result-of-stop-form-action-tab-0')).toHaveClass('active');

    // Click on box
    fireEvent.click(getByTestId('ripa-result-of-stop-form-box-0-0'));

    // Box active
    await waitFor(() => {
      expect(getByTestId('ripa-result-of-stop-form-box-0-0')).toHaveClass('active');
    });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // Snack is thrown
    await waitFor(() => {
      expect(dispatchCustomEvent).toBeCalledWith('create-snack-notification',
        {
          body: 'Must add at least 1 code for each reason of stop',
          level: { icon: 'alert-error.svg', type: 'error' },
          title: 'Result of stop required',
        }),
        { timeout: 2000 };
    });

    // Click on box
    fireEvent.click(getByTestId('ripa-result-of-stop-form-any-results-switch'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 5.1
    await waitFor(() => {
      expect(getByTestId('dynamic-select-form-legacy')).toBeInTheDocument();
    });
  });

  it('updates years of experience without error message', async () => {
    request.mockImplementation(() => Promise.resolve({}));

    const store = createStore();

    // Set dispatch mock for snack
    window.dispatchEvent = jest.fn();

    store.dispatch(Form.setFormField({ path: ['flags'] })({ pre2024: false, post2024: false }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))
    store.dispatch(Form.setFormField({ path: ['flags'] })({ pre2024: false }));
    store.dispatch(NewReportDuck.setStep({ step: 0, substep: 0, subloop: 0, component: Steps[0].substeps[0].component }));
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(setTemplateLoading(false));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // Start on 1.1
    expect(getByTestId('ripa-calendar-form')).toBeInTheDocument();

    // Click to open the User Settings to update year of experience for 2024
    if (store.getState().Form.flags.pre2024) {

      // Open the User Settings Dialog
      fireEvent.click(getByTestId('new-report-drawer-user-settings'));

      // Click up button in year of experience
      fireEvent.click(getByTestId('user-settings-dialog-years-of-experience-controls-up'));

      // No error snack created
      await waitFor(() => {
        expect(window.dispatchEvent).toBeCalledTimes(0);
      });

       // Click down button in year of experience
       fireEvent.click(getByTestId('user-settings-dialog__years-of-experience-controls-down'));

    } else {
      // Click up button in year of experience
      fireEvent.click(getByTestId('new-report-drawer__years-of-experience-controls-down'));

      // No error snack created
      await waitFor(() => {
        expect(window.dispatchEvent).toBeCalledTimes(0);
      });

      // Click down button in year of experience
      fireEvent.click(getByTestId('new-report-drawer__years-of-experience-controls-down'));
    }

    // No error snack created
    await waitFor(() => {
      expect(window.dispatchEvent).toBeCalledTimes(0);
    });
  });

  it('shows number of contacts when moving to the first substep of each step', async () => {
    request.mockImplementation(() => Promise.resolve({}))
    const store = createStore();

    // Set to last substep of step 3
    store.dispatch(NewReportDuck.setStep({ step: 2, substep: 0, subloop: -1, component: Steps[2].substeps[0].component }));

    // Set normal steps
    store.dispatch(NewReportDuck.registerSteps({
      nSteps: Steps.map(({ substeps, title, loop }) => (
        { title, loop, nSubsteps: substeps.length, preconditions: substeps.map(s => s.precondition) }
      )),
      steps: Steps,
    }));
    store.dispatch(User.setUser({ organization: { state: 'CA' } }))
    store.dispatch(User.setOrganizationInfo({ defaultTemplateName: 'CA_doj_ripa' }))

    store.dispatch(setTemplateLoading(false));

    // Set number of people
    store.dispatch(Form.setFormField({ path: ['numberOfPeople'] })(1));

    const { getByTestId } = render(
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={store}>
          <MemoryRouter initialEntries={['/new-report']} initialIndex={0}>
            <NewReport />
          </MemoryRouter>
        </Provider>
      </MuiPickersUtilsProvider>
    );

    // 3.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-primary-reason-form')).toBeInTheDocument();
    });

    expect(getByTestId('new-report-drawer-info-suspects-num')).toHaveTextContent(/^1$/)

    // Select consensual encounter
    fireEvent.click(getByTestId('ripa-primary-reason-form-box-4'));

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 3.5 appears
    await waitFor(() => {
      expect(getByTestId('ripa-description-form')).toBeInTheDocument();
    });

    const ripaDescriptionFormInput = getByTestId('ripa-description-form-input');

    expect(ripaDescriptionFormInput).toBeInTheDocument();

    fireEvent.change(ripaDescriptionFormInput, { target: { value: 'Description' } });

    // Click continue
    fireEvent.click(getByTestId('ripa-form-container-continue'));

    // 4.1 appears
    await waitFor(() => {
      expect(getByTestId('ripa-action-taken-form')).toBeInTheDocument();
    });

    expect(getByTestId('new-report-drawer-info-suspects-num')).toHaveTextContent(/^1$/)
  });
});
