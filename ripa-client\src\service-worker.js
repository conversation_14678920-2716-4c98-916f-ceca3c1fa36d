// This service worker can be customized!
// See https://developers.google.com/web/tools/workbox/modules
// for the list of available Workbox modules, or add any other
// code you'd like.
// You can also remove this file if you'd prefer not to use a
// service worker, and the Workbox build step will be skipped.

import dayjs from 'dayjs'
import qs from 'qs'
import { clientsClaim } from 'workbox-core';
import { ExpirationPlugin } from 'workbox-expiration';
import { precache, matchPrecache, createHandlerBoundToURL } from 'workbox-precaching';
import { createInstanceSafariFix } from '@utility/createInstanceSafariFix'
import { registerRoute } from 'workbox-routing';
import { StaleWhileRevalidate } from 'workbox-strategies';
import { customAlphabet } from 'nanoid'
import { SyncRedactor } from '@utility/redact-pii';

const generateId = () => `ol-${customAlphabet('1234567890qwertyuioplkjhgfdsazxcvbnm', 10)()}`

const swResponse = (data, status) => new Response(typeof data === 'string' ? data : JSON.stringify(data), { status, headers: { 'x-from-service-worker': 'true' } })

const redactor = new SyncRedactor();

const offlineFormUpdates = createInstanceSafariFix({
  name: 'contact_offline_form_updates',
  storeName: 'contact_offline_form_updates',
  version: 1.0
});

const offlineFormCreation = createInstanceSafariFix({
  name: 'contact_offline_form_creation',
  storeName: 'contact_offline_form_creation',
  version: 1.0
});

const offlineTemplates = createInstanceSafariFix({
  name: 'contact_offline_templates',
  storeName: 'contact_offline_templates',
  version: 1.0
});

const offlineConfig = createInstanceSafariFix({
  name: 'contact_offline_config',
  storeName: 'contact_offline_config',
  version: 1.0
}, {
  'work-offline': false
});

export const offlineCustomQuestions = createInstanceSafariFix({
  name: 'contact_offline_custom_questions',
  storeName: 'contact_offline_custom_questions',
  version: 1.0
})

clientsClaim();

// Precache all of the assets generated by your build process.
// Their URLs are injected into the manifest variable below.
// This variable must be present somewhere in your service worker file,
// even if you decide not to use precaching.

precache(self.__WB_MANIFEST);

// Commit hash is critical for updates - updatefound event is fired, forcing a window reload
console.log('ServiceWorker Boot', COMMIT_HASH)

// Set up App Shell-style routing, so that all navigation requests
// are fulfilled with your index.html shell.
const fileExtensionRegexp = new RegExp('/[^/?]+\\.[^/]+$');
registerRoute(
  // Return false to exempt requests from being fulfilled by index.html.
  ({ request, url }) => {
    if (url.pathname.includes('users/saml/sign_in')) {
      return false
    }

    // Async job management panel
    if (url.pathname.startsWith('/rails/good_job')) {
      return false;
    }

    // System settings panel
    if (url.pathname.startsWith('/rails/system_settings')) {
      return false;
    }

    // If this isn't a navigation, skip.
    if (request.mode !== 'navigate') {
      return false;
    } // If this is a URL that starts with /_, skip.

    if (url.pathname.startsWith('/_')) {
      return false;
    } // If this looks like a URL for a resource, because it contains // a file extension, skip.

    if (url.pathname.match(fileExtensionRegexp)) {
      return false;
    } // Return true to signal that we want to use the handler.

    return true;
  },
  createHandlerBoundToURL(`${process.env.PUBLIC_URL}/index.html`)
);

// An example runtime caching route for requests that aren't handled by the
// precache, in this case same-origin .png requests like those from in public/
registerRoute(
  // Add in any other file extensions or routing criteria as needed.
  ({ url }) => url.origin === self.location.origin && url.pathname.endsWith('.png'), // Customize this strategy as needed, e.g., by changing to CacheFirst.
  new StaleWhileRevalidate({
    cacheName: 'images',
    plugins: [
      // Ensure that once this runtime cache reaches a maximum size the
      // least-recently used images are removed.
      new ExpirationPlugin({ maxEntries: 50 }),
    ],
  })
);

self.addEventListener('install', function (event) {
  event.waitUntil(self.skipWaiting());
  // Cache any static files on install event
  event.waitUntil(
    caches.open('data').then(function (cache) {
      return cache.addAll(
        [
          `${process.env.PUBLIC_URL}/config.json`,
          `${process.env.PUBLIC_URL}/manifest.json`,
          `${process.env.PUBLIC_URL}/schools.js`,
          `${process.env.PUBLIC_URL}/codes.js`,
          `${process.env.PUBLIC_URL}/cities.js`,
          `${process.env.PUBLIC_URL}/icon-font.css`,
          `${process.env.PUBLIC_URL}/material-icons-v107.woff2`
        ]
      );
    })
  );
});

// fetch the resource from the network
const fromNetwork = (request, includeCredentials, timeout) =>
  new Promise((resolve, reject) => {
    const timeoutId = setTimeout(reject, timeout);
    fetch(request, { credentials: includeCredentials ? 'include' : 'omit' }).then(response => {
      clearTimeout(timeoutId);
      resolve(response);
    }).catch(reject);
  });

self.addEventListener('fetch', (event) => {
  const fetchLogic = async () => {
    try {
      const { url, headers } = event.request.clone();
      const isVeritoneRequest = url.includes('veritone.com') || url.includes('contact-dev.com');
      const forceNoCredentials = headers.get('x-no-cred') === 'true';
      const includeCredentials = isVeritoneRequest && !forceNoCredentials;

      // For the case of testing the network connection, or logging in while in offline mode,
      // check for x-use-network to circumvent custom fetch logic
      if (headers.get('x-use-network') === 'true') {
        return fetch(event.request.clone(), { credentials: includeCredentials ? 'include' : 'omit' })
          .catch(() => null); // Catch the error so it doesnt spam the console when requests fail because of no network
      }

      // For the case of retreiving offline forms while online
      // Prevents the need for duplicating the fetch logic from indexedDb in the case of GET form
      if (headers.get('x-use-storage') === 'true') {
        throw new Error('Go work offline');
      }

      const workOffline = await offlineConfig.getItem('work-offline').then(r => r === 'true');

      // If we are working offline, api we want to send back new responses for api routes
      // Otherwise fetch routes should be executed so they hit cache
      if (workOffline && url.match(/\/api\/v.\//)) {
        throw new Error('Go work offline');
      }

      // Attempt to get network data (network first)
      const networkResponse = await fromNetwork(event.request.clone(), includeCredentials, 40000).catch(() => null)
      if (networkResponse) {
        return networkResponse;
      }

      // Attempt to get from cache
      return matchPrecache(event.request.clone())
        .then(matchedRequest => matchedRequest ?? caches.open('data')
          .then(cache => cache.match(event.request.clone())))
    } catch (error) {
      try {
        const { url, method } = event.request.clone();

        // Return available latest template from storage
        if (url.match(/\/api\/v.\/form_templates\?latest=true/) && method === 'GET') {
          const templates = [];
          await offlineTemplates.iterate(t => { templates.push(t) });

          return swResponse(templates.sort((a, b) => {
            if (a?.name && b?.name && a?.version && b?.version) {
              const lc = a.name.localeCompare(b.name);
              if (lc === 0) {
                return -a.version.localeCompare(b.version);
              }
              return lc;
            }
            return 0;
          }), 200)
        }

        // Return template by ID from storage
        if (url.match(/\/api\/v.\/form_templates\/[0-9]+/) && method === 'GET') {
          const templateId = url.split('/').reverse()[0];

          if (templateId) {
            const template = await offlineTemplates.getItem(templateId);
            return swResponse({ ...template, id: templateId }, 200)
          }
        }

        // Perform regex redaction on pii textfields
        if (url.match(/\/api\/v.\/form_pii_redaction.*relation=mine/) && method === 'GET') {
          const { textChars } = qs.parse(url.split('?').reverse()[0]) ?? { textChars: '', };
          try {
            const { textToRedact, matches } = redactor.redact(textChars);
            return swResponse({ text: textToRedact, matches }, 200)
          } catch (e) {
            return swResponse(`Something went wrong while redacting text: ${e.message}`, 500)
          }
        }

        // Return 200 on delete user (logout)
        if (url.match(/\/api\/v.\/users\/sign_out/) && method === 'DELETE') {
          return swResponse('', 200)
        }

        // Return 401 on session
        if (url.match(/\/api\/v.\/users\/session/) && method === 'GET') {
          const userConfigRes = await offlineConfig.getItem('user')
          return swResponse(userConfigRes, 401)
        }

        // Return 200 on delete user (logout)
        if (url.match(/\/api\/v.\/set_offline_status\/[-0-9A-z]+/) && method === 'PATCH') {
          const { offlineStatus, onlineId } = await event.request.clone().json();
          const formId = url.split('/').reverse()[0];
          const formRecord = await offlineFormUpdates.getItem(formId);

          await offlineFormUpdates.setItem(formId, { ...formRecord, offlineStatus, onlineId });

          return swResponse('', 200)
        }

        // Return custom questions for offline mode
        if (url.match(/\/api\/v.\/organizations\/[0-9]+\/custom_questions/) && method === 'GET') {
          const customQuestions = await offlineCustomQuestions.getItem('questions')

          if (customQuestions) {
            return swResponse({ questions: customQuestions }, 200)
          }
          return swResponse({ message: 'Custom questions not currently in storage' }, 404)
        }

        // Remove offline form after synced
        if (url.match(/\/api\/v.\/remove_offline_synced\/[-0-9A-z]+/) && method === 'DELETE') {
          const formId = url.split('/').reverse()[0];

          await offlineFormUpdates.removeItem(formId);
          await offlineFormCreation.removeItem(formId);

          return swResponse('', 200)
        }

        // Return paginated forms from the storage
        if (url.match(/\/api\/v.\/form_data.*relation=mine/) && method === 'GET') {
          let { page, limit } = qs.parse(url.split('?').reverse()[0]) ?? { page: 1, limit: 10 };
          let forms = [];

          page = Number(page);
          limit = Number(limit);
          try {
            await offlineFormCreation.iterate(createdForm => { forms.push(createdForm) });
            await offlineFormUpdates.iterate((formUpdates, formUpdatesId) => {
              const createdFormIndex = forms.findIndex(f => f.id === formUpdatesId);
              if (createdFormIndex >= 0) {
                const form = forms[createdFormIndex]
                // If the form was created and updated offline, then the updated form will not have form_template_id or id
                forms.splice(createdFormIndex, 1, { ...formUpdates, form_template_id: form.form_template_id, id: form.id });
              } else {
                forms.push(formUpdates);
              }
            });

            const count = forms.length;
            forms.sort((a, b) => dayjs(b.updated_at).valueOf() - dayjs(a.updated_at).valueOf());
            forms = forms.slice((page - 1) * limit, limit * page);
            const res = { forms, pagination: { count, page, items: forms.length, pages: Math.ceil(count / limit) } };

            return swResponse(res, 200)
          } catch (e) {
            return swResponse(`Something went wrong while getting forms: ${e.message}`, 500)
          }
        }

        // Return isValidTime check from storage
        if (url.match(/\/api\/v\d+\/form_data\/[-0-9A-z]+\/valid_time\/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/) && method === 'GET') {
          const urlObj = new URL(url);

          // Extract path segments
          const params = urlObj.pathname.split('/');
          const formId = params[4];
          const dateTime = params[6];

          // Get all form ids
          const allForms = await offlineFormCreation.keys();
          let isUnique = true;

          const formPromises = allForms
            .filter((id) => id !== formId) // Exclude the current formId
            .map((id) => offlineFormCreation.getItem(id)); // Fetch the forms by id

          const forms = await Promise.all(formPromises);

          // Check if any form has the same stopDateTime
          isUnique = !forms.some((form) => form && form.contents && form.contents.stopDateTime.slice(0, 16) === dateTime.slice(0, 16));

          return swResponse({ message: isUnique ? 'valid date time' : 'conflicting date time' }, isUnique ? 200 : 409);
        }

        // Return form by ID from storage
        if (url.match(/\/api\/v.\/form_data\/[-0-9A-z]+/) && method === 'GET') {
          const formId = url.split('/').reverse()[0];
          const form = await offlineFormCreation.getItem(formId);
          let formUpdates = await offlineFormUpdates.getItem(formId);

          if (formUpdates) {
            if (form) {
              // If the form was created and updated offline, then the updated form will not have form_template_id or id
              formUpdates.contents.offlineCreatedAt = form.contents.offlineCreatedAt;
              formUpdates = { ...formUpdates, form_template_id: formUpdates.form_template_id ?? form.form_template_id, id: form.id };
            }

            // formUpdates holds the latest version of the form if there was an update
            return swResponse(formUpdates, 200)
          }
          if (form) {
            // form holds the version of the form during creation
            return swResponse(form, 200)
          }

          return swResponse({ message: 'Form not available while offline' }, 404)
        }

        // Save form changes
        if (url.match(/\/api\/v.\/form_data/) && method === 'PATCH') {
          const { form_template_id, contents, status, offlineStatus } = await event.request.clone().json();
          const formId = url.split('/').reverse()[0];
          const record = { form_template_id, id: formId, contents, status, offlineStatus: offlineStatus ?? 'offline', updated_at: dayjs().toISOString() };
          offlineFormUpdates.setItem(formId, record, e => e && console.error('Failed to save form update', e));

          return swResponse('', 201)
        }

        // Delete form
        if (url.match(/\/api\/v.\/form_data\/[-0-9A-z]+/) && method === 'DELETE') {
          const formId = url.split('/').reverse()[0];
          try {
            const offlineFormUpdatesKeys = await offlineFormUpdates.keys();
            const offlineFormCreationKeys = await offlineFormCreation.keys();
            const hasUpdates = offlineFormUpdatesKeys.includes(formId)
            const hasCreation = offlineFormCreationKeys.includes(formId);

            if (hasUpdates) {
              await offlineFormUpdates.removeItem(formId);
            }
            if (hasCreation) {
              await offlineFormCreation.removeItem(formId);
            }
            if (!hasUpdates && !hasCreation) {
              return swResponse({ message: 'Form not available while offline' }, 404)
            }
            return swResponse('', 201)
          } catch (e) {
            return swResponse(`Something went wrong while deleting form: ${e.message}`, 500)
          }
        }

        // Statistics
        if (url.match(/\/api\/v.\/statistics\/forms\?relation=mine/) && method === 'GET') {
          try {
            const offlineForms = {};
            await offlineFormUpdates.iterate((form, key) => {
              offlineForms[key] = form;
            });
            await offlineFormCreation.iterate((form, key) => {
              if (offlineForms[key]) {
                offlineForms[key] = { ...offlineForms[key], ...form }
              } else {
                offlineForms[key] = form;
              }
            });

            const formsWithContent = Object.values(offlineForms).filter(f => !!f.contents).length;

            return swResponse({ statistics: { offlineForms: formsWithContent } }, 200)
          } catch (e) {
            return swResponse(`Something went wrong while deleting form: ${e.message}`, 500)
          }
        }

        // Save form creation
        if (url.match(/\/api\/v.\/form_data/) && method === 'POST') {
          const { contents, form_template_id, status } = await event.request.clone().json();
          const offlineId = generateId();
          contents.offlineCreatedAt = dayjs().toISOString();
          const record = { id: offlineId, contents, status, offlineStatus: 'offline', form_template_id, updated_at: dayjs().toISOString() };

          try {
            await offlineFormCreation.setItem(offlineId, record);
            return swResponse({ id: offlineId }, 201)
          } catch (e) {
            return swResponse(`Something went wrong while creating form: ${e.message}`, 500)
          }
        }

        console.error('No api match', url, error)

        return error;
      } catch (er) {
        console.error('SW offline routes error', er)
      }
    }
  };

  try {
    if (!event.request.url.includes('/rails/')) {
      event.respondWith(fetchLogic());
    }
  } catch (e) {
    console.error('SW respondWith error', e)
  }
});
