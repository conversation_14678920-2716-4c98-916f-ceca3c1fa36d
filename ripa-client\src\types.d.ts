interface Window {
  readonly analytics: unknown;
  readonly pendo?: {
    initialize: (config: {
      visitor: {
        id: string | number;
        email: string;
      };
      account: {
        id: string | number;
        name: string;
      };
    }) => void;
  };
  readonly Cypress: unknown;
  readonly google: unknown;
  // TODO: Do we even need this?
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly __veritoneAppSingleton: any; // IVeritoneApp<any, any, any>;
  buildDetails: {
    hash?: string;
    hashdate?: string;
    builddate?: string;
  };
  cities?: {
    State: string;
    City: string;
    County: string;
    InactiveDate?: string;
  }[] | null;
  schools?: {
    CDSCode: string;
    StatusType: string;
    County: string;
    District: string;
    School: string;
    Street: string;
    StreetAbr: string;
    City: string;
    Zip: string;
    State: string;
  }[] | null;
  offenseCodes?: {
    Code: number;
    Statute: string;
    Literal: string;
    TypeOfCharge: string;
    TypeOfStatute?: string;
    Repealed?: string;
    OffenseRepealed?: string; // Legacy
    label: string; // Added during pre-processing
  }[] | null;
  postDate2024: string;
  allow2024Switch: true;
  forceEarlyPost2024Switch: true;
  config: unknown;
  store: unknown;
  readonly __REDUX_DEVTOOLS_EXTENSION_COMPOSE__: unknown; // compose
  aiware: unknown;
  setPosition?: (e: Element) => void;
  shouldRenderForm: {
    steps: unknown;
    formLoading: boolean;
    isTemplateLoading: boolean;
  };
  readonly testEnums?: {
    id?: number;
    name?: string;
    answers?: Array<Enum>;
    version?: string;
    formTemplateId?: number;
  };
  authDebug?: unknown;
  addEventListener<K extends keyof CustomEventMap>(type: K,
    listener: (this: Window, ev: CustomEventMap[K]) => void): void;
  removeEventListener<K extends keyof CustomEventMap>(type: K,
    listener: (this: Window, ev: CustomEventMap[K]) => void): void;
}

interface Document {
  addEventListener<K extends keyof CustomEventMap>(type: K,
    listener: (this: Document, ev: CustomEventMap[K]) => void): void;
  removeEventListener<K extends keyof CustomEventMap>(type: K,
    listener: (this: Document, ev: CustomEventMap[K]) => void): void;
}

interface String {
  toTitleCase(): string;
}

// Extend Navigator to include effectiveType - https://developer.mozilla.org/en-US/docs/Web/API/NetworkInformation/effectiveType
interface Navigator {
  readonly connection?: {
    readonly effectiveType?: string;
  };
}

declare const COMMIT_HASH: string;

declare const LASTCOMMITDATETIME: string;

declare const BUILD_DATE: string;

interface Array<T> {
  last(): T;
}

interface Console {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  stdlog: (...args: any) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  stdErrorLog: (...args: any) => void;
  logs: unknown[];
  errors: unknown[];
}

interface Enum {
  csvColumn: {
    header: string;
    position: number;
  };
  defaultValue: unknown;
  description: string;
  jsonStructure: {
    Age: string;
  };
  type: string;
}

declare module '*.scss' {
  const selectors: Record<string, string>;
  export default selectors;
}

declare module '*.svg' {
  const image: string;
  export default image;
}

declare module 'redux-api-middleware' {
  const apiMiddleware: unknown;
}

declare module '*.png';

declare module 'redux-mock-store';

declare module 'react-test-renderer';

declare module 'memoize-one';

declare module 'inobounce';

type KeysMatching<T, V> = { [K in keyof T]-?: T[K] extends V ? K : never }[keyof T];

interface CustomEventMap {
  'decoupled-dispatch': CustomEvent<unknown>;
}

type ValueOf<T> = T[keyof T];
