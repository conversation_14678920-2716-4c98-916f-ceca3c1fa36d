import sortAlphaNum from '@utility/sortAlphaNum'
import dayjs from 'dayjs'

// TODO: Is there anything we can do to make this sorting more generic/type-safe?
type Row<T = unknown> = Record<string, T>

const sortText = (key: string) => (rowA: Row, rowB: Row) => sortAlphaNum(rowA[key] as string, rowB[key] as string)

const sortTime = (key: string) => (rowA: Row, rowB: Row) => dayjs(rowA[key] as string) > dayjs(rowB[key] as string) ? 1 : -1

const sortNumber = (key: string) => (rowA: Row, rowB: Row) => (rowA[key] as number) > (rowB[key] as number) ? 1 : -1

const sortBool = (key: string) => (rowA: Row, rowB: Row) => (rowA[key] as boolean) === (rowB[key] as boolean) ? 1 : -1

export { sortText, sortTime, sortNumber, sortBool }
